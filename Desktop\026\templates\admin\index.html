{% extends "base.html" %}

{% block title %}لوحة الإدارة - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">لوحة الإدارة</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cogs me-2"></i>
        لوحة الإدارة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.new_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-1"></i>
                مستخدم جديد
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2" style="border-right: 4px solid #4e73df;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي المستخدمين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_users }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2" style="border-right: 4px solid #1cc88a;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            المستخدمين النشطين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active_users }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2" style="border-right: 4px solid #36b9cc;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            إجمالي الرسائل
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_messages }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-envelope fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2" style="border-right: 4px solid #f6c23e;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            الأقسام
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_departments }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- آخر الأنشطة -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-history me-2"></i>
                    آخر الأنشطة
                </h6>
                <a href="{{ url_for('admin.logs') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_actions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>الإجراء</th>
                                <th>الوصف</th>
                                <th>التوقيت</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for action in recent_actions %}
                            <tr>
                                <td>
                                    <strong>{{ action.user.full_name }}</strong><br>
                                    <small class="text-muted">{{ action.user.department }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ action.get_action_display() }}</span>
                                </td>
                                <td>{{ action.description or 'لا يوجد وصف' }}</td>
                                <td>
                                    <small>{{ action.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد أنشطة حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- المستخدمين الجدد -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    المستخدمين الجدد
                </h6>
            </div>
            <div class="card-body">
                {% if new_users %}
                {% for user in new_users %}
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <strong>{{ user.full_name }}</strong><br>
                        <small class="text-muted">{{ user.department }}</small><br>
                        <small class="text-muted">{{ user.created_at.strftime('%Y-%m-%d') }}</small>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-user-plus fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا يوجد مستخدمين جدد</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- روابط سريعة للإدارة -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-link me-2"></i>
                    روابط سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('admin.users') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2 text-primary"></i>
                        إدارة المستخدمين
                    </a>
                    <a href="{{ url_for('admin.departments') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-building me-2 text-success"></i>
                        إدارة الأقسام
                    </a>
                    <a href="{{ url_for('admin.message_types') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tags me-2 text-warning"></i>
                        أنواع الرسائل
                    </a>
                    {% if current_user.role == 'admin' %}
                    <a href="{{ url_for('admin.permissions_management') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt me-2 text-danger"></i>
                        إدارة الصلاحيات
                    </a>
                    <a href="{{ url_for('admin.settings') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog me-2 text-info"></i>
                        إعدادات النظام
                    </a>
                    <a href="{{ url_for('admin.logs') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-history me-2 text-secondary"></i>
                        سجل الأنشطة
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات النظام -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>إصدار النظام:</h6>
                        <p class="text-muted">1.0.0</p>
                    </div>
                    <div class="col-md-3">
                        <h6>آخر تحديث:</h6>
                        <p class="text-muted">اليوم</p>
                    </div>
                    <div class="col-md-3">
                        <h6>حالة النظام:</h6>
                        <span class="badge bg-success">يعمل بشكل طبيعي</span>
                    </div>
                    <div class="col-md-3">
                        <h6>وقت التشغيل:</h6>
                        <p class="text-muted">متصل</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }

    .border-right-primary {
        border-right: 4px solid #4e73df !important;
    }

    .border-right-success {
        border-right: 4px solid #1cc88a !important;
    }

    .border-right-info {
        border-right: 4px solid #36b9cc !important;
    }

    .border-right-warning {
        border-right: 4px solid #f6c23e !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الإحصائيات كل دقيقة
    setInterval(function() {
        // يمكن إضافة تحديث تلقائي للإحصائيات هنا
    }, 60000);
</script>
{% endblock %}
