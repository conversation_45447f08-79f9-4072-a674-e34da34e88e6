from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(50), nullable=False, default='employee')  # admin, manager, employee, secretary
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # العلاقات
    sent_messages = db.relationship('Message', foreign_keys='Message.sender_id', backref='sender', lazy='dynamic')
    received_messages = db.relationship('Message', foreign_keys='Message.recipient_id', backref='recipient', lazy='dynamic')
    actions = db.relationship('ActionLog', backref='user', lazy='dynamic')

    def set_password(self, password):
        """تشفير كلمة المرور"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)

    def has_permission(self, permission):
        """التحقق من الصلاحيات"""
        permissions = {
            'admin': ['create', 'read', 'update', 'delete', 'manage_users', 'view_reports'],
            'manager': ['create', 'read', 'update', 'view_reports'],
            'secretary': ['create', 'read', 'update'],
            'employee': ['read']
        }
        return permission in permissions.get(self.role, [])

    def __repr__(self):
        return f'<User {self.username}>'

class Department(db.Model):
    """نموذج الإدارات والأقسام"""
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    description = db.Column(db.Text)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    manager = db.relationship('User', backref='managed_department')

    def __repr__(self):
        return f'<Department {self.name}>'

class MessageType(db.Model):
    """نموذج أنواع الرسائل"""
    __tablename__ = 'message_types'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    name_en = db.Column(db.String(50))
    color = db.Column(db.String(7), default='#007bff')  # لون hex
    priority = db.Column(db.Integer, default=1)  # 1=عادي, 2=مهم, 3=عاجل
    is_active = db.Column(db.Boolean, default=True)

    def __repr__(self):
        return f'<MessageType {self.name}>'

class Message(db.Model):
    """نموذج الرسائل والمراسلات"""
    __tablename__ = 'messages'

    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(50), unique=True, nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text)

    # معلومات المرسل والمستقبل
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    external_sender = db.Column(db.String(100))  # للرسائل الخارجية
    external_recipient = db.Column(db.String(100))  # للرسائل الخارجية

    # تصنيف الرسالة
    message_type_id = db.Column(db.Integer, db.ForeignKey('message_types.id'))
    direction = db.Column(db.String(20), nullable=False)  # incoming, outgoing, internal
    priority = db.Column(db.String(20), default='normal')  # normal, important, urgent
    confidentiality = db.Column(db.String(20), default='normal')  # normal, confidential, secret

    # حالة الرسالة
    status = db.Column(db.String(20), default='new')  # new, in_progress, completed, archived

    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    received_date = db.Column(db.DateTime)
    due_date = db.Column(db.DateTime)
    completed_date = db.Column(db.DateTime)

    # معلومات إضافية
    notes = db.Column(db.Text)
    tags = db.Column(db.String(200))  # tags مفصولة بفواصل

    # العلاقات
    message_type = db.relationship('MessageType', backref='messages')
    attachments = db.relationship('Attachment', backref='message', lazy='dynamic', cascade='all, delete-orphan')
    actions = db.relationship('ActionLog', backref='message', lazy='dynamic', cascade='all, delete-orphan')

    def generate_reference_number(self):
        """توليد رقم مرجعي تلقائي"""
        year = datetime.now().year
        count = Message.query.filter(
            db.extract('year', Message.created_at) == year
        ).count() + 1
        self.reference_number = f"{year}/{count:04d}"

    def get_status_display(self):
        """عرض حالة الرسالة بالعربية"""
        status_map = {
            'new': 'جديد',
            'in_progress': 'قيد المعالجة',
            'completed': 'مكتمل',
            'archived': 'مؤرشف'
        }
        return status_map.get(self.status, self.status)

    def get_priority_display(self):
        """عرض أولوية الرسالة بالعربية"""
        priority_map = {
            'normal': 'عادي',
            'important': 'مهم',
            'urgent': 'عاجل'
        }
        return priority_map.get(self.priority, self.priority)

    def __repr__(self):
        return f'<Message {self.reference_number}: {self.subject}>'

class Attachment(db.Model):
    """نموذج المرفقات"""
    __tablename__ = 'attachments'

    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)  # بالبايت
    mime_type = db.Column(db.String(100))
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    uploader = db.relationship('User', backref='uploaded_files')

    def get_file_size_display(self):
        """عرض حجم الملف بشكل مقروء"""
        if not self.file_size:
            return "غير محدد"

        size = self.file_size
        units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
        unit_index = 0

        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1

        return f"{size:.1f} {units[unit_index]}"

    def __repr__(self):
        return f'<Attachment {self.original_filename}>'

class ActionLog(db.Model):
    """نموذج سجل الإجراءات"""
    __tablename__ = 'action_logs'

    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # created, updated, forwarded, completed, etc.
    description = db.Column(db.Text)
    old_value = db.Column(db.Text)  # القيمة القديمة (JSON)
    new_value = db.Column(db.Text)  # القيمة الجديدة (JSON)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def get_action_display(self):
        """عرض الإجراء بالعربية"""
        action_map = {
            'created': 'إنشاء',
            'updated': 'تحديث',
            'forwarded': 'إحالة',
            'completed': 'إكمال',
            'archived': 'أرشفة',
            'deleted': 'حذف',
            'viewed': 'مشاهدة',
            'downloaded': 'تحميل',
            'printed': 'طباعة'
        }
        return action_map.get(self.action, self.action)

    def __repr__(self):
        return f'<ActionLog {self.action} by {self.user.username}>'

class Setting(db.Model):
    """نموذج الإعدادات العامة"""
    __tablename__ = 'settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.String(255))
    category = db.Column(db.String(50), default='general')
    is_public = db.Column(db.Boolean, default=False)  # هل يمكن للمستخدمين العاديين رؤيتها
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @staticmethod
    def get_setting(key, default=None):
        """الحصول على قيمة إعداد"""
        setting = Setting.query.filter_by(key=key).first()
        return setting.value if setting else default

    @staticmethod
    def set_setting(key, value, description=None, category='general'):
        """تعيين قيمة إعداد"""
        setting = Setting.query.filter_by(key=key).first()
        if setting:
            setting.value = value
            setting.updated_at = datetime.utcnow()
        else:
            setting = Setting(
                key=key,
                value=value,
                description=description,
                category=category
            )
            db.session.add(setting)
        db.session.commit()
        return setting

    def __repr__(self):
        return f'<Setting {self.key}: {self.value}>'
