from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed, FileRequired
from wtforms import StringField, TextAreaField, SelectField, DateTimeField, PasswordField, BooleanField, HiddenField, MultipleFileField
from wtforms.validators import DataRequired, Length, Email, EqualTo, Optional
from wtforms.widgets import TextArea
from models import User, Department, MessageType

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني')

class UserForm(FlaskForm):
    """نموذج إضافة/تعديل المستخدمين"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=100)])
    department = StringField('القسم/الإدارة', validators=[DataRequired(), Length(max=100)])
    role = SelectField('الدور', choices=[
        ('employee', 'موظف'),
        ('secretary', 'سكرتير'),
        ('manager', 'مدير'),
        ('admin', 'مدير النظام')
    ], validators=[DataRequired()])
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور', 
                                   validators=[EqualTo('password', message='كلمات المرور غير متطابقة')])
    is_active = BooleanField('نشط', default=True)

class ChangePasswordForm(FlaskForm):
    """نموذج تغيير كلمة المرور"""
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور الجديدة', 
                                   validators=[DataRequired(), EqualTo('new_password', message='كلمات المرور غير متطابقة')])

class MessageForm(FlaskForm):
    """نموذج إضافة/تعديل الرسائل"""
    subject = StringField('الموضوع', validators=[DataRequired(), Length(max=200)])
    content = TextAreaField('المحتوى', widget=TextArea())
    
    # معلومات المرسل والمستقبل
    recipient_id = SelectField('المرسل إليه', coerce=int, validators=[Optional()])
    external_sender = StringField('المرسل الخارجي', validators=[Optional(), Length(max=100)])
    external_recipient = StringField('المستقبل الخارجي', validators=[Optional(), Length(max=100)])
    
    # تصنيف الرسالة
    message_type_id = SelectField('نوع الرسالة', coerce=int, validators=[Optional()])
    direction = SelectField('اتجاه الرسالة', choices=[
        ('incoming', 'واردة'),
        ('outgoing', 'صادرة'),
        ('internal', 'داخلية')
    ], validators=[DataRequired()])
    priority = SelectField('الأولوية', choices=[
        ('normal', 'عادي'),
        ('important', 'مهم'),
        ('urgent', 'عاجل')
    ], default='normal')
    confidentiality = SelectField('السرية', choices=[
        ('normal', 'عادي'),
        ('confidential', 'سري'),
        ('secret', 'سري جداً')
    ], default='normal')
    
    # التواريخ
    received_date = DateTimeField('تاريخ الاستلام', validators=[Optional()])
    due_date = DateTimeField('تاريخ الاستحقاق', validators=[Optional()])
    
    # معلومات إضافية
    notes = TextAreaField('ملاحظات')
    tags = StringField('العلامات (مفصولة بفواصل)', validators=[Optional(), Length(max=200)])
    
    # المرفقات
    attachments = MultipleFileField('المرفقات', validators=[
        FileAllowed(['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'jpg', 'jpeg', 'png', 'gif'], 
                   'نوع الملف غير مدعوم!')
    ])
    
    def __init__(self, *args, **kwargs):
        super(MessageForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات المستخدمين
        self.recipient_id.choices = [(0, 'اختر المستقبل')] + [
            (user.id, f"{user.full_name} ({user.department})") 
            for user in User.query.filter_by(is_active=True).all()
        ]
        
        # تحديث خيارات أنواع الرسائل
        self.message_type_id.choices = [(0, 'اختر نوع الرسالة')] + [
            (msg_type.id, msg_type.name) 
            for msg_type in MessageType.query.filter_by(is_active=True).all()
        ]

class SearchForm(FlaskForm):
    """نموذج البحث المتقدم"""
    search_query = StringField('البحث في الموضوع والمحتوى')
    reference_number = StringField('الرقم المرجعي')
    sender_id = SelectField('المرسل', coerce=int, validators=[Optional()])
    recipient_id = SelectField('المستقبل', coerce=int, validators=[Optional()])
    message_type_id = SelectField('نوع الرسالة', coerce=int, validators=[Optional()])
    direction = SelectField('اتجاه الرسالة', choices=[
        ('', 'الكل'),
        ('incoming', 'واردة'),
        ('outgoing', 'صادرة'),
        ('internal', 'داخلية')
    ])
    priority = SelectField('الأولوية', choices=[
        ('', 'الكل'),
        ('normal', 'عادي'),
        ('important', 'مهم'),
        ('urgent', 'عاجل')
    ])
    status = SelectField('الحالة', choices=[
        ('', 'الكل'),
        ('new', 'جديد'),
        ('in_progress', 'قيد المعالجة'),
        ('completed', 'مكتمل'),
        ('archived', 'مؤرشف')
    ])
    date_from = DateTimeField('من تاريخ', validators=[Optional()])
    date_to = DateTimeField('إلى تاريخ', validators=[Optional()])
    
    def __init__(self, *args, **kwargs):
        super(SearchForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات المستخدمين
        users_choices = [(0, 'الكل')] + [
            (user.id, f"{user.full_name} ({user.department})") 
            for user in User.query.filter_by(is_active=True).all()
        ]
        self.sender_id.choices = users_choices
        self.recipient_id.choices = users_choices
        
        # تحديث خيارات أنواع الرسائل
        self.message_type_id.choices = [(0, 'الكل')] + [
            (msg_type.id, msg_type.name) 
            for msg_type in MessageType.query.filter_by(is_active=True).all()
        ]

class DepartmentForm(FlaskForm):
    """نموذج إضافة/تعديل الأقسام"""
    name = StringField('اسم القسم', validators=[DataRequired(), Length(max=100)])
    name_en = StringField('الاسم بالإنجليزية', validators=[Optional(), Length(max=100)])
    description = TextAreaField('الوصف')
    manager_id = SelectField('المدير', coerce=int, validators=[Optional()])
    is_active = BooleanField('نشط', default=True)
    
    def __init__(self, *args, **kwargs):
        super(DepartmentForm, self).__init__(*args, **kwargs)
        self.manager_id.choices = [(0, 'بدون مدير')] + [
            (user.id, user.full_name) 
            for user in User.query.filter_by(is_active=True).all()
        ]

class MessageTypeForm(FlaskForm):
    """نموذج إضافة/تعديل أنواع الرسائل"""
    name = StringField('اسم النوع', validators=[DataRequired(), Length(max=50)])
    name_en = StringField('الاسم بالإنجليزية', validators=[Optional(), Length(max=50)])
    color = StringField('اللون', validators=[Optional(), Length(max=7)], default='#007bff')
    priority = SelectField('الأولوية', choices=[
        (1, 'عادي'),
        (2, 'مهم'),
        (3, 'عاجل')
    ], coerce=int, default=1)
    is_active = BooleanField('نشط', default=True)

class ActionForm(FlaskForm):
    """نموذج إضافة إجراء على الرسالة"""
    action = SelectField('الإجراء', choices=[
        ('forwarded', 'إحالة'),
        ('completed', 'إكمال'),
        ('archived', 'أرشفة'),
        ('updated', 'تحديث')
    ], validators=[DataRequired()])
    description = TextAreaField('وصف الإجراء', validators=[DataRequired()])
    new_status = SelectField('الحالة الجديدة', choices=[
        ('new', 'جديد'),
        ('in_progress', 'قيد المعالجة'),
        ('completed', 'مكتمل'),
        ('archived', 'مؤرشف')
    ], validators=[Optional()])
