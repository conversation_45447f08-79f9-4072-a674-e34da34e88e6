from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc, func
from datetime import datetime

from models import db, User, Message, Department, MessageType, Setting, ActionLog, RolePermission
from forms import UserForm, DepartmentForm, MessageTypeForm

bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات الإدارة"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role not in ['admin', 'manager']:
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def index():
    """لوحة تحكم الإدارة"""
    # إحصائيات سريعة
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    total_messages = Message.query.count()
    total_departments = Department.query.count()

    # آخر الأنشطة
    recent_actions = ActionLog.query.order_by(desc(ActionLog.created_at)).limit(10).all()

    # المستخدمين الجدد
    new_users = User.query.order_by(desc(User.created_at)).limit(5).all()

    stats = {
        'total_users': total_users,
        'active_users': active_users,
        'total_messages': total_messages,
        'total_departments': total_departments
    }

    return render_template('admin/index.html',
                         stats=stats,
                         recent_actions=recent_actions,
                         new_users=new_users)

# إدارة المستخدمين
@bp.route('/users')
@login_required
@admin_required
def users():
    """قائمة المستخدمين"""
    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/users.html', users=users)

@bp.route('/users/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_user():
    """إضافة مستخدم جديد"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة مستخدمين جدد', 'error')
        return redirect(url_for('admin.users'))

    form = UserForm()

    if form.validate_on_submit():
        # التحقق من عدم وجود اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter(
            (User.username == form.username.data) |
            (User.email == form.email.data)
        ).first()

        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
        else:
            user = User(
                username=form.username.data,
                email=form.email.data,
                full_name=form.full_name.data,
                department=form.department.data,
                role=form.role.data,
                is_active=form.is_active.data
            )

            if form.password.data:
                user.set_password(form.password.data)
            else:
                user.set_password('123456')  # كلمة مرور افتراضية

            db.session.add(user)
            db.session.commit()

            flash('تم إضافة المستخدم بنجاح', 'success')
            return redirect(url_for('admin.users'))

    return render_template('admin/new_user.html', form=form)

@bp.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(id):
    """تعديل المستخدم"""
    user = User.query.get_or_404(id)

    if current_user.role != 'admin' and user.id != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذا المستخدم', 'error')
        return redirect(url_for('admin.users'))

    form = UserForm(obj=user)

    if form.validate_on_submit():
        # التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter(
            ((User.username == form.username.data) |
             (User.email == form.email.data)) &
            (User.id != user.id)
        ).first()

        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
        else:
            user.username = form.username.data
            user.email = form.email.data
            user.full_name = form.full_name.data
            user.department = form.department.data
            user.role = form.role.data
            user.is_active = form.is_active.data

            if form.password.data:
                user.set_password(form.password.data)

            db.session.commit()
            flash('تم تحديث المستخدم بنجاح', 'success')
            return redirect(url_for('admin.users'))

    return render_template('admin/edit_user.html', form=form, user=user)

@bp.route('/users/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(id):
    """حذف المستخدم"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف المستخدمين', 'error')
        return redirect(url_for('admin.users'))

    user = User.query.get_or_404(id)

    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('admin.users'))

    # التحقق من وجود رسائل مرتبطة بالمستخدم
    message_count = Message.query.filter(
        (Message.sender_id == user.id) | (Message.recipient_id == user.id)
    ).count()

    if message_count > 0:
        # تعطيل المستخدم بدلاً من حذفه
        user.is_active = False
        db.session.commit()
        flash('تم تعطيل المستخدم بدلاً من حذفه لوجود رسائل مرتبطة به', 'warning')
    else:
        db.session.delete(user)
        db.session.commit()
        flash('تم حذف المستخدم بنجاح', 'success')

    return redirect(url_for('admin.users'))

@bp.route('/users/<int:id>/permissions')
@login_required
@admin_required
def user_permissions(id):
    """عرض صلاحيات المستخدم"""
    user = User.query.get_or_404(id)
    return render_template('admin/user_permissions.html', user=user)

@bp.route('/users/<int:id>/reset-password', methods=['POST'])
@login_required
@admin_required
def reset_user_password(id):
    """إعادة تعيين كلمة مرور المستخدم"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإعادة تعيين كلمات المرور', 'error')
        return redirect(url_for('admin.users'))

    user = User.query.get_or_404(id)

    # توليد كلمة مرور مؤقتة
    import secrets
    import string
    temp_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))

    user.set_password(temp_password)
    db.session.commit()

    flash(f'تم إعادة تعيين كلمة المرور. كلمة المرور الجديدة: {temp_password}', 'success')
    return redirect(url_for('admin.user_permissions', id=id))

@bp.route('/permissions')
@login_required
@admin_required
def permissions_management():
    """إدارة الصلاحيات العامة"""
    # إحصائيات المستخدمين حسب الدور
    admin_count = User.query.filter_by(role='admin').count()
    manager_count = User.query.filter_by(role='manager').count()
    secretary_count = User.query.filter_by(role='secretary').count()
    employee_count = User.query.filter_by(role='employee').count()
    total_users = User.query.count()

    return render_template('admin/permissions_management.html',
                         admin_count=admin_count,
                         manager_count=manager_count,
                         secretary_count=secretary_count,
                         employee_count=employee_count,
                         total_users=total_users)

@bp.route('/permissions/roles')
@login_required
@admin_required
def role_permissions():
    """إدارة صلاحيات الأدوار"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة صلاحيات الأدوار', 'error')
        return redirect(url_for('admin.permissions_management'))

    # إحصائيات المستخدمين حسب الدور
    admin_count = User.query.filter_by(role='admin').count()
    manager_count = User.query.filter_by(role='manager').count()
    secretary_count = User.query.filter_by(role='secretary').count()
    employee_count = User.query.filter_by(role='employee').count()

    # الحصول على الصلاحيات الحالية من قاعدة البيانات
    current_permissions = RolePermission.get_all_role_permissions()

    # تهيئة الصلاحيات الافتراضية إذا لم تكن موجودة
    if not any(current_permissions.values()):
        RolePermission.initialize_default_permissions()
        current_permissions = RolePermission.get_all_role_permissions()

    return render_template('admin/role_permissions.html',
                         admin_count=admin_count,
                         manager_count=manager_count,
                         secretary_count=secretary_count,
                         employee_count=employee_count,
                         current_permissions=current_permissions)

@bp.route('/permissions/update-roles', methods=['POST'])
@login_required
@admin_required
def update_role_permissions():
    """تحديث صلاحيات الأدوار"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية لتحديث الصلاحيات'})

    try:
        permissions_data = request.get_json()

        # تحديث الصلاحيات في قاعدة البيانات
        for role, permissions in permissions_data.items():
            # الحصول على جميع الصلاحيات المتاحة
            all_permissions = [
                'messages_create', 'messages_read', 'messages_update', 'messages_delete',
                'messages_archive', 'messages_export', 'messages_import',
                'users_create', 'users_read', 'users_update', 'users_delete',
                'users_activate', 'users_permissions', 'users_reset_password',
                'departments_create', 'departments_read', 'departments_update', 'departments_delete',
                'message_types_create', 'message_types_read', 'message_types_update', 'message_types_delete',
                'reports_view', 'reports_export', 'reports_advanced',
                'system_settings', 'system_logs', 'system_backup', 'system_maintenance',
                'view_all_messages', 'manage_all_departments', 'access_admin_panel',
                'view_department_messages', 'manage_department_users'
            ]

            # تحديث كل صلاحية
            for permission in all_permissions:
                is_granted = permission in permissions
                RolePermission.set_role_permission(role, permission, is_granted)

        # تسجيل النشاط
        action_log = ActionLog(
            user_id=current_user.id,
            action='update_role_permissions',
            description=f'تم تحديث صلاحيات الأدوار الوظيفية: {", ".join(permissions_data.keys())}',
            ip_address=request.remote_addr
        )
        db.session.add(action_log)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديث الصلاحيات بنجاح وحفظها في قاعدة البيانات'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/permissions/reset-defaults', methods=['POST'])
@login_required
@admin_required
def reset_default_permissions():
    """إعادة تعيين الصلاحيات للإعدادات الافتراضية"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية لإعادة تعيين الصلاحيات'})

    try:
        # إعادة تهيئة الصلاحيات الافتراضية
        RolePermission.initialize_default_permissions()

        # تسجيل النشاط
        action_log = ActionLog(
            user_id=current_user.id,
            action='reset_default_permissions',
            description='تم إعادة تعيين صلاحيات الأدوار للإعدادات الافتراضية',
            ip_address=request.remote_addr
        )
        db.session.add(action_log)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إعادة تعيين الصلاحيات للإعدادات الافتراضية بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/permissions/matrix')
@login_required
@admin_required
def permissions_matrix():
    """مصفوفة صلاحيات الأدوار التفاعلية"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة مصفوفة الصلاحيات', 'error')
        return redirect(url_for('admin.permissions_management'))

    # إحصائيات المستخدمين حسب الدور
    admin_count = User.query.filter_by(role='admin').count()
    manager_count = User.query.filter_by(role='manager').count()
    secretary_count = User.query.filter_by(role='secretary').count()
    employee_count = User.query.filter_by(role='employee').count()

    # الحصول على الصلاحيات الحالية من قاعدة البيانات
    current_permissions = RolePermission.get_all_role_permissions()

    # تهيئة الصلاحيات الافتراضية إذا لم تكن موجودة
    if not any(current_permissions.values()):
        RolePermission.initialize_default_permissions()
        current_permissions = RolePermission.get_all_role_permissions()

    return render_template('admin/permissions_matrix.html',
                         admin_count=admin_count,
                         manager_count=manager_count,
                         secretary_count=secretary_count,
                         employee_count=employee_count,
                         current_permissions=current_permissions)

# إدارة الأقسام
@bp.route('/departments')
@login_required
@admin_required
def departments():
    """قائمة الأقسام"""
    departments = Department.query.order_by(Department.name).all()
    return render_template('admin/departments.html', departments=departments)

@bp.route('/departments/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_department():
    """إضافة قسم جديد"""
    form = DepartmentForm()

    if form.validate_on_submit():
        department = Department(
            name=form.name.data,
            name_en=form.name_en.data,
            description=form.description.data,
            manager_id=form.manager_id.data if form.manager_id.data else None,
            is_active=form.is_active.data
        )

        db.session.add(department)
        db.session.commit()

        flash('تم إضافة القسم بنجاح', 'success')
        return redirect(url_for('admin.departments'))

    return render_template('admin/new_department.html', form=form)

@bp.route('/departments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_department(id):
    """تعديل القسم"""
    department = Department.query.get_or_404(id)
    form = DepartmentForm(obj=department)

    if form.validate_on_submit():
        department.name = form.name.data
        department.name_en = form.name_en.data
        department.description = form.description.data
        department.manager_id = form.manager_id.data if form.manager_id.data else None
        department.is_active = form.is_active.data

        db.session.commit()
        flash('تم تحديث القسم بنجاح', 'success')
        return redirect(url_for('admin.departments'))

    return render_template('admin/edit_department.html', form=form, department=department)

# إدارة أنواع الرسائل
@bp.route('/message-types')
@login_required
@admin_required
def message_types():
    """قائمة أنواع الرسائل"""
    message_types = MessageType.query.order_by(MessageType.priority.desc(), MessageType.name).all()
    return render_template('admin/message_types.html', message_types=message_types)

@bp.route('/message-types/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_message_type():
    """إضافة نوع رسالة جديد"""
    form = MessageTypeForm()

    if form.validate_on_submit():
        message_type = MessageType(
            name=form.name.data,
            name_en=form.name_en.data,
            color=form.color.data,
            priority=form.priority.data,
            is_active=form.is_active.data
        )

        db.session.add(message_type)
        db.session.commit()

        flash('تم إضافة نوع الرسالة بنجاح', 'success')
        return redirect(url_for('admin.message_types'))

    return render_template('admin/new_message_type.html', form=form)

@bp.route('/settings')
@login_required
@admin_required
def settings():
    """إعدادات النظام"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى الإعدادات', 'error')
        return redirect(url_for('admin.index'))

    settings_list = Setting.query.order_by(Setting.category, Setting.key).all()
    settings = {setting.key: setting.value for setting in settings_list}
    return render_template('admin/settings.html', settings=settings)

@bp.route('/logs')
@login_required
@admin_required
def logs():
    """سجل الأنشطة"""
    page = request.args.get('page', 1, type=int)
    logs = ActionLog.query.order_by(desc(ActionLog.created_at)).paginate(
        page=page, per_page=50, error_out=False
    )
    return render_template('admin/logs.html', logs=logs)
