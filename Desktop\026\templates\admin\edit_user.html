{% extends "base.html" %}

{% block title %}تعديل المستخدم - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.users') }}">إدارة المستخدمين</a></li>
        <li class="breadcrumb-item active">تعديل {{ user.full_name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل المستخدم: {{ user.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للمستخدمين
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-circle me-2"></i>
                    تعديل بيانات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}

                    <!-- المعلومات الأساسية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label required") }}
                            {{ form.full_name(class="form-control", required=True) }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.full_name.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label required") }}
                            {{ form.username(class="form-control", required=True) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">اسم المستخدم لتسجيل الدخول</div>
                        </div>
                    </div>

                    <!-- البريد الإلكتروني والقسم -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label required") }}
                            {{ form.email(class="form-control", required=True) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.department.label(class="form-label required") }}
                            {{ form.department(class="form-control", required=True) }}
                            {% if form.department.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.department.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الدور والحالة -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.role.label(class="form-label required") }}
                            {{ form.role(class="form-select", required=True) }}
                            {% if form.role.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.role.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if current_user.role != 'admin' %}
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    لا يمكنك تغيير الدور
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة الحساب</label>
                            <div class="form-check form-switch">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                            {% if user.id == current_user.id %}
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    لا يمكنك تعطيل حسابك الخاص
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- كلمة المرور الجديدة -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-key me-2"></i>تغيير كلمة المرور</h6>
                        <p class="mb-0">اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control", placeholder="كلمة المرور الجديدة (اختياري)") }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.confirm_password.label(class="form-label") }}
                            {{ form.confirm_password(class="form-control", placeholder="تأكيد كلمة المرور الجديدة") }}
                            {% if form.confirm_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.confirm_password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- معلومات المستخدم -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المستخدم
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="avatar-circle mx-auto mb-3" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user fa-2x text-white"></i>
                </div>

                <h5>{{ user.full_name }}</h5>
                <p class="text-muted">{{ user.department }}</p>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary">{{ user.sent_messages|length }}</h6>
                            <small class="text-muted">رسائل مرسلة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success">{{ user.received_messages|length }}</h6>
                        <small class="text-muted">رسائل مستقبلة</small>
                    </div>
                </div>

                <hr>

                <div class="text-start">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-2"></i>
                        عضو منذ: {{ user.created_at.strftime('%Y-%m-%d') }}
                    </small><br>
                    {% if user.last_login %}
                    <small class="text-muted">
                        <i class="fas fa-clock me-2"></i>
                        آخر دخول: {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                    </small>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- إحصائيات النشاط -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات النشاط
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الرسائل المرسلة:</span>
                        <span class="badge bg-primary">{{ user.sent_messages|length }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الرسائل المستقبلة:</span>
                        <span class="badge bg-success">{{ user.received_messages|length }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الإجراءات المنفذة:</span>
                        <span class="badge bg-info">{{ user.actions|length if user.actions else 0 }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الملفات المرفوعة:</span>
                        <span class="badge bg-warning">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- تحذيرات -->
        {% if user.id == current_user.id %}
        <div class="card shadow mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيه
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0">
                    أنت تقوم بتعديل حسابك الخاص.
                    كن حذراً عند تغيير البيانات الأساسية.
                </p>
            </div>
        </div>
        {% endif %}

        {% if current_user.role == 'admin' and user.role == 'admin' and user.id != current_user.id %}
        <div class="card shadow mt-4">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    مدير نظام
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0">
                    هذا المستخدم مدير نظام.
                    تأكد من صحة التغييرات قبل الحفظ.
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .required::after {
        content: " *";
        color: red;
    }

    .avatar-circle {
        transition: transform 0.3s ease;
    }

    .avatar-circle:hover {
        transform: scale(1.05);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // التحقق من صحة النموذج
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // التحقق من تطابق كلمات المرور
        const passwordField = document.getElementById('password');
        const confirmPasswordField = document.getElementById('confirm_password');

        function checkPasswordMatch() {
            if (passwordField.value && confirmPasswordField.value) {
                if (passwordField.value === confirmPasswordField.value) {
                    confirmPasswordField.setCustomValidity('');
                } else {
                    confirmPasswordField.setCustomValidity('كلمات المرور غير متطابقة');
                }
            } else {
                confirmPasswordField.setCustomValidity('');
            }
        }

        passwordField.addEventListener('input', checkPasswordMatch);
        confirmPasswordField.addEventListener('input', checkPasswordMatch);

        // منع تعطيل الحساب الخاص
        {% if user.id == current_user.id %}
        const isActiveField = document.getElementById('is_active');
        isActiveField.addEventListener('change', function() {
            if (!this.checked) {
                this.checked = true;
                alert('لا يمكنك تعطيل حسابك الخاص');
            }
        });
        {% endif %}

        // منع تغيير الدور إذا لم يكن مدير نظام
        {% if current_user.role != 'admin' %}
        const roleField = document.getElementById('role');
        roleField.disabled = true;
        {% endif %}
    });
</script>
{% endblock %}
