{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item active">إدارة المستخدمين</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة المستخدمين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin.new_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-1"></i>
                مستخدم جديد
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- قائمة المستخدمين -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المستخدمين ({{ users.total }} مستخدم)
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="filterUsers('all')">
                        الكل
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="filterUsers('active')">
                        النشطين
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="filterUsers('inactive')">
                        غير النشطين
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                {% if users.items %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>القسم</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users.items %}
                            <tr class="user-row" data-status="{{ 'active' if user.is_active else 'inactive' }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <strong>{{ user.full_name }}</strong><br>
                                            <small class="text-muted">{{ user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge bg-info">{{ user.department }}</span>
                                </td>
                                <td>
                                    <span class="badge
                                        {% if user.role == 'admin' %}bg-danger
                                        {% elif user.role == 'manager' %}bg-warning
                                        {% elif user.role == 'secretary' %}bg-success
                                        {% else %}bg-secondary{% endif %}">
                                        {% if user.role == 'admin' %}مدير النظام
                                        {% elif user.role == 'manager' %}مدير
                                        {% elif user.role == 'secretary' %}سكرتير
                                        {% else %}موظف{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ user.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    {% if user.last_login %}
                                        <small>{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                                    {% else %}
                                        <small class="text-muted">لم يسجل دخول</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if current_user.role == 'admin' or user.id == current_user.id %}
                                        <a href="{{ url_for('admin.edit_user', id=user.id) }}"
                                           class="btn btn-outline-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}

                                        {% if current_user.role == 'admin' and user.id != current_user.id %}
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="deleteUser({{ user.id }}, '{{ user.full_name }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- التصفح -->
                {% if users.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="تصفح المستخدمين">
                        <ul class="pagination justify-content-center mb-0">
                            {% if users.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in users.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != users.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.users', page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=users.next_num) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>

                    <div class="text-center mt-2">
                        <small class="text-muted">
                            عرض {{ users.per_page * (users.page - 1) + 1 }} -
                            {{ users.per_page * (users.page - 1) + users.items|length }}
                            من أصل {{ users.total }} مستخدم
                        </small>
                    </div>
                </div>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا يوجد مستخدمين</h4>
                    <p class="text-muted">لم يتم إنشاء أي مستخدمين بعد</p>
                    {% if current_user.role == 'admin' %}
                    <a href="{{ url_for('admin.new_user') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i>
                        إنشاء مستخدم جديد
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات المستخدمين -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ users.total }}</h5>
                <p class="card-text">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">1</h5>
                <p class="card-text">المستخدمين النشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">1</h5>
                <p class="card-text">مديري النظام</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">0</h5>
                <p class="card-text">المديرين</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }

    .user-row {
        transition: all 0.3s ease;
    }

    .user-row:hover {
        background-color: rgba(0,123,255,0.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // فلترة المستخدمين
    function filterUsers(status) {
        const rows = document.querySelectorAll('.user-row');

        rows.forEach(row => {
            if (status === 'all') {
                row.style.display = '';
            } else {
                if (row.dataset.status === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });

        // تحديث الأزرار
        document.querySelectorAll('.btn-group .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    // حذف مستخدم
    function deleteUser(userId, userName) {
        if (confirm(`هل أنت متأكد من حذف المستخدم "${userName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي للحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/users/${userId}/delete`;

            // إضافة CSRF token
            const csrfToken = document.querySelector('meta[name=csrf-token]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';
                csrfInput.value = csrfToken.getAttribute('content');
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    }

    // البحث في المستخدمين
    function searchUsers() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const rows = document.querySelectorAll('.user-row');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // إضافة حقل البحث
    document.addEventListener('DOMContentLoaded', function() {
        const cardHeader = document.querySelector('.card-header');
        const searchDiv = document.createElement('div');
        searchDiv.className = 'col-md-4';
        searchDiv.innerHTML = `
            <div class="input-group">
                <input type="text" class="form-control" id="search-input" placeholder="البحث في المستخدمين..." onkeyup="searchUsers()">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
            </div>
        `;

        // إضافة البحث إلى الهيدر
        const headerContent = cardHeader.innerHTML;
        cardHeader.innerHTML = `
            <div class="row align-items-center w-100">
                <div class="col-md-8">
                    ${headerContent}
                </div>
                <div class="col-md-4">
                    ${searchDiv.innerHTML}
                </div>
            </div>
        `;
    });
</script>
{% endblock %}
