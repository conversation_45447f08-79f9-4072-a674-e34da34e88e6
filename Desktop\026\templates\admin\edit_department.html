{% extends "base.html" %}

{% block title %}تعديل القسم - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.departments') }}">إدارة الأقسام</a></li>
        <li class="breadcrumb-item active">تعديل {{ department.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>
        تعديل القسم: {{ department.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.departments') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للأقسام
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    تعديل بيانات القسم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <!-- CSRF protection will be handled by Flask-WTF if needed -->

                    <!-- المعلومات الأساسية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label required">اسم القسم</label>
                            <input type="text" class="form-control" name="name" required
                                   value="{{ department.name }}">
                            <div class="invalid-feedback">
                                يرجى إدخال اسم القسم
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم بالإنجليزية</label>
                            <input type="text" class="form-control" name="name_en"
                                   value="{{ department.name_en or '' }}">
                            <div class="form-text">اختياري - للاستخدام في التقارير</div>
                        </div>
                    </div>

                    <!-- الوصف -->
                    <div class="mb-3">
                        <label class="form-label">وصف القسم</label>
                        <textarea class="form-control" name="description" rows="3">{{ department.description or '' }}</textarea>
                        <div class="form-text">وصف اختياري يساعد في تحديد مهام ونشاطات القسم</div>
                    </div>

                    <!-- المدير -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مدير القسم</label>
                            <select class="form-select" name="manager_id">
                                <option value="">اختر مدير القسم</option>
                                <!-- سيتم تحميل المديرين ديناميكياً -->
                                <option value="1" {{ 'selected' if department.manager_id == 1 }}>المدير العام</option>
                            </select>
                            <div class="form-text">يمكن تغيير المدير في أي وقت</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">الموقع</label>
                            <input type="text" class="form-control" name="location"
                                   value="{{ department.location or '' }}">
                            <div class="form-text">موقع القسم في المبنى</div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone"
                                   value="{{ department.phone or '' }}">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email"
                                   value="{{ department.email or '' }}">
                        </div>
                    </div>

                    <!-- الحالة -->
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="is_active"
                                   {{ 'checked' if department.is_active }}>
                            <label class="form-check-label">القسم نشط</label>
                        </div>
                        <div class="form-text">تعطيل القسم سيمنع ظهوره في القوائم الجديدة</div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.departments') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- معلومات القسم -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات القسم
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="avatar-circle mx-auto mb-3" style="width: 80px; height: 80px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-building fa-2x text-white"></i>
                </div>

                <h5>{{ department.name }}</h5>
                {% if department.name_en %}
                <p class="text-muted">{{ department.name_en }}</p>
                {% endif %}

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary">0</h6>
                            <small class="text-muted">الموظفين</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success">0</h6>
                        <small class="text-muted">الرسائل</small>
                    </div>
                </div>

                <hr>

                <div class="text-start">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-2"></i>
                        تم الإنشاء: {{ department.created_at.strftime('%Y-%m-%d') if department.created_at else 'غير محدد' }}
                    </small><br>
                    {% if department.location %}
                    <small class="text-muted">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        الموقع: {{ department.location }}
                    </small><br>
                    {% endif %}
                    {% if department.phone %}
                    <small class="text-muted">
                        <i class="fas fa-phone me-2"></i>
                        الهاتف: {{ department.phone }}
                    </small><br>
                    {% endif %}
                    {% if department.email %}
                    <small class="text-muted">
                        <i class="fas fa-envelope me-2"></i>
                        البريد: {{ department.email }}
                    </small>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- إحصائيات النشاط -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات النشاط
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>عدد الموظفين:</span>
                        <span class="badge bg-primary">0</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الرسائل المرسلة:</span>
                        <span class="badge bg-success">0</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الرسائل المستقبلة:</span>
                        <span class="badge bg-info">0</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الحالة:</span>
                        {% if department.is_active %}
                            <span class="badge bg-success">نشط</span>
                        {% else %}
                            <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewEmployees()">
                        <i class="fas fa-users me-1"></i>
                        عرض الموظفين
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="viewMessages()">
                        <i class="fas fa-envelope me-1"></i>
                        رسائل القسم
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="generateReport()">
                        <i class="fas fa-chart-line me-1"></i>
                        تقرير القسم
                    </button>
                </div>
            </div>
        </div>

        <!-- تحذيرات -->
        {% if not department.is_active %}
        <div class="card shadow mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيه
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0">
                    هذا القسم غير نشط حالياً.
                    لن يظهر في القوائم الجديدة للمستخدمين.
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .required::after {
        content: " *";
        color: red;
    }

    .avatar-circle {
        transition: transform 0.3s ease;
    }

    .avatar-circle:hover {
        transform: scale(1.05);
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // التحقق من صحة النموذج
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // التحقق من اسم القسم
        const nameField = document.querySelector('input[name="name"]');
        nameField.addEventListener('blur', function() {
            const name = this.value.trim();
            if (name.length >= 2) {
                this.classList.add('is-valid');
            }
        });

        // التحقق من البريد الإلكتروني
        const emailField = document.querySelector('input[name="email"]');
        emailField.addEventListener('blur', function() {
            const email = this.value.trim();
            if (email === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                this.classList.add('is-valid');
            }
        });
    });

    // الإجراءات السريعة
    function viewEmployees() {
        // توجيه لصفحة موظفي القسم
        window.location.href = `/admin/departments/{{ department.id }}/employees`;
    }

    function viewMessages() {
        // توجيه لصفحة رسائل القسم
        window.location.href = `/messages?department={{ department.id }}`;
    }

    function generateReport() {
        // توجيه لصفحة تقرير القسم
        window.location.href = `/reports/department/{{ department.id }}`;
    }
</script>
{% endblock %}
