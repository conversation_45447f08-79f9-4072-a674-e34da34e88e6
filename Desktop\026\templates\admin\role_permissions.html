{% extends "base.html" %}

{% block title %}إدارة صلاحيات الأدوار - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.permissions_management') }}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item active">تحرير صلاحيات الأدوار</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-cog me-2"></i>
        إدارة صلاحيات الأدوار الوظيفية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-success" onclick="saveAllPermissions()">
                <i class="fas fa-save me-1"></i>
                حفظ جميع التغييرات
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetPermissions()">
                <i class="fas fa-undo me-1"></i>
                إعادة تعيين
            </button>
        </div>
    </div>
</div>

<!-- تحذير هام -->
<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>تحذير:</strong> تغيير صلاحيات الأدوار سيؤثر على جميع المستخدمين المرتبطين بهذه الأدوار.
    تأكد من مراجعة التغييرات بعناية قبل الحفظ.
</div>

<!-- علامات التبويب للأدوار -->
<ul class="nav nav-tabs" id="rolesTab" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="admin-tab" data-bs-toggle="tab" data-bs-target="#admin" type="button" role="tab">
            <i class="fas fa-crown me-2"></i>
            مدير النظام
            <span class="badge bg-danger ms-2">{{ admin_count }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="manager-tab" data-bs-toggle="tab" data-bs-target="#manager" type="button" role="tab">
            <i class="fas fa-user-tie me-2"></i>
            مدير
            <span class="badge bg-success ms-2">{{ manager_count }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="secretary-tab" data-bs-toggle="tab" data-bs-target="#secretary" type="button" role="tab">
            <i class="fas fa-user-edit me-2"></i>
            سكرتير
            <span class="badge bg-warning ms-2">{{ secretary_count }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="employee-tab" data-bs-toggle="tab" data-bs-target="#employee" type="button" role="tab">
            <i class="fas fa-user me-2"></i>
            موظف
            <span class="badge bg-info ms-2">{{ employee_count }}</span>
        </button>
    </li>
</ul>

<!-- محتوى علامات التبويب -->
<div class="tab-content" id="rolesTabContent">
    {% set all_permissions = {
        'messages': {
            'name': 'إدارة الرسائل',
            'icon': 'fas fa-envelope',
            'permissions': {
                'messages_create': 'إنشاء رسائل',
                'messages_read': 'قراءة الرسائل',
                'messages_update': 'تعديل الرسائل',
                'messages_delete': 'حذف الرسائل',
                'messages_archive': 'أرشفة الرسائل',
                'messages_export': 'تصدير الرسائل',
                'messages_import': 'استيراد الرسائل'
            }
        },
        'users': {
            'name': 'إدارة المستخدمين',
            'icon': 'fas fa-users',
            'permissions': {
                'users_create': 'إنشاء مستخدمين',
                'users_read': 'عرض المستخدمين',
                'users_update': 'تعديل المستخدمين',
                'users_delete': 'حذف المستخدمين',
                'users_activate': 'تفعيل/تعطيل المستخدمين',
                'users_permissions': 'إدارة صلاحيات المستخدمين',
                'users_reset_password': 'إعادة تعيين كلمة المرور'
            }
        },
        'departments': {
            'name': 'إدارة الأقسام',
            'icon': 'fas fa-building',
            'permissions': {
                'departments_create': 'إنشاء أقسام',
                'departments_read': 'عرض الأقسام',
                'departments_update': 'تعديل الأقسام',
                'departments_delete': 'حذف الأقسام'
            }
        },
        'message_types': {
            'name': 'إدارة أنواع الرسائل',
            'icon': 'fas fa-tags',
            'permissions': {
                'message_types_create': 'إنشاء أنواع رسائل',
                'message_types_read': 'عرض أنواع الرسائل',
                'message_types_update': 'تعديل أنواع الرسائل',
                'message_types_delete': 'حذف أنواع الرسائل'
            }
        },
        'reports': {
            'name': 'التقارير',
            'icon': 'fas fa-chart-line',
            'permissions': {
                'reports_view': 'عرض التقارير',
                'reports_export': 'تصدير التقارير',
                'reports_advanced': 'التقارير المتقدمة'
            }
        },
        'system': {
            'name': 'إدارة النظام',
            'icon': 'fas fa-cogs',
            'permissions': {
                'system_settings': 'إعدادات النظام',
                'system_logs': 'سجلات النظام',
                'system_backup': 'النسخ الاحتياطي',
                'system_maintenance': 'صيانة النظام'
            }
        },
        'special': {
            'name': 'صلاحيات خاصة',
            'icon': 'fas fa-star',
            'permissions': {
                'view_all_messages': 'عرض جميع الرسائل',
                'manage_all_departments': 'إدارة جميع الأقسام',
                'access_admin_panel': 'الوصول للوحة الإدارة',
                'view_department_messages': 'عرض رسائل القسم',
                'manage_department_users': 'إدارة مستخدمي القسم'
            }
        }
    } %}

    {% set role_permissions = current_permissions if current_permissions else {
        'admin': ['messages_create', 'messages_read', 'messages_update', 'messages_delete', 'messages_archive', 'messages_export', 'messages_import', 'users_create', 'users_read', 'users_update', 'users_delete', 'users_activate', 'users_permissions', 'users_reset_password', 'departments_create', 'departments_read', 'departments_update', 'departments_delete', 'message_types_create', 'message_types_read', 'message_types_update', 'message_types_delete', 'reports_view', 'reports_export', 'reports_advanced', 'system_settings', 'system_logs', 'system_backup', 'system_maintenance', 'view_all_messages', 'manage_all_departments', 'access_admin_panel'],
        'manager': ['messages_create', 'messages_read', 'messages_update', 'messages_archive', 'messages_export', 'users_read', 'users_update', 'departments_read', 'departments_update', 'message_types_read', 'reports_view', 'reports_export', 'view_department_messages', 'manage_department_users'],
        'secretary': ['messages_create', 'messages_read', 'messages_update', 'messages_archive', 'users_read', 'departments_read', 'message_types_read', 'reports_view'],
        'employee': ['messages_create', 'messages_read', 'users_read', 'departments_read', 'message_types_read']
    } %}

    {% for role, role_name in [('admin', 'مدير النظام'), ('manager', 'مدير'), ('secretary', 'سكرتير'), ('employee', 'موظف')] %}
    <div class="tab-pane fade {{ 'show active' if role == 'admin' }}" id="{{ role }}" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    صلاحيات دور: {{ role_name }}
                </h5>
            </div>
            <div class="card-body">
                <form id="form-{{ role }}">
                    {% for category_key, category in all_permissions.items() %}
                    <div class="permission-category mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <i class="{{ category.icon }} me-2 text-primary"></i>
                            <h6 class="mb-0 me-3">{{ category.name }}</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input category-toggle" type="checkbox"
                                       id="toggle-{{ role }}-{{ category_key }}"
                                       onchange="toggleCategory('{{ role }}', '{{ category_key }}')">
                                <label class="form-check-label" for="toggle-{{ role }}-{{ category_key }}">
                                    تحديد الكل
                                </label>
                            </div>
                        </div>

                        <div class="row">
                            {% for perm_key, perm_name in category.permissions.items() %}
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input permission-checkbox"
                                           type="checkbox"
                                           id="{{ role }}-{{ perm_key }}"
                                           name="{{ role }}_permissions"
                                           value="{{ perm_key }}"
                                           data-category="{{ category_key }}"
                                           {{ 'checked' if perm_key in role_permissions[role] }}
                                           onchange="updateCategoryToggle('{{ role }}', '{{ category_key }}')">
                                    <label class="form-check-label" for="{{ role }}-{{ perm_key }}">
                                        {{ perm_name }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}

                    <!-- ملخص الصلاحيات -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>ملخص الصلاحيات:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>إجمالي الصلاحيات المحددة:</strong>
                                <span id="total-{{ role }}" class="badge bg-primary">0</span>
                            </div>
                            <div class="col-md-6">
                                <strong>عدد المستخدمين المتأثرين:</strong>
                                <span class="badge bg-warning">
                                    {% if role == 'admin' %}{{ admin_count }}
                                    {% elif role == 'manager' %}{{ manager_count }}
                                    {% elif role == 'secretary' %}{{ secretary_count }}
                                    {% else %}{{ employee_count }}{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- أزرار الإجراءات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">إجراءات سريعة</h6>
                        <small class="text-muted">تطبيق إعدادات سريعة على الأدوار</small>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="applyTemplate('restrictive')">
                            <i class="fas fa-lock me-1"></i>
                            نموذج مقيد
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="applyTemplate('balanced')">
                            <i class="fas fa-balance-scale me-1"></i>
                            نموذج متوازن
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="applyTemplate('permissive')">
                            <i class="fas fa-unlock me-1"></i>
                            نموذج مرن
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .permission-category {
        border-left: 4px solid #007bff;
        padding-left: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    .category-toggle:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    .nav-tabs .nav-link {
        border-radius: 8px 8px 0 0;
    }

    .nav-tabs .nav-link.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .badge {
        font-size: 0.75em;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث عداد الصلاحيات
    function updatePermissionCount(role) {
        const checkboxes = document.querySelectorAll(`input[name="${role}_permissions"]:checked`);
        document.getElementById(`total-${role}`).textContent = checkboxes.length;
    }

    // تبديل تحديد فئة كاملة
    function toggleCategory(role, category) {
        const toggle = document.getElementById(`toggle-${role}-${category}`);
        const checkboxes = document.querySelectorAll(`input[data-category="${category}"][name="${role}_permissions"]`);

        checkboxes.forEach(checkbox => {
            checkbox.checked = toggle.checked;
        });

        updatePermissionCount(role);
    }

    // تحديث حالة مفتاح الفئة
    function updateCategoryToggle(role, category) {
        const checkboxes = document.querySelectorAll(`input[data-category="${category}"][name="${role}_permissions"]`);
        const checkedBoxes = document.querySelectorAll(`input[data-category="${category}"][name="${role}_permissions"]:checked`);
        const toggle = document.getElementById(`toggle-${role}-${category}`);

        if (checkedBoxes.length === checkboxes.length) {
            toggle.checked = true;
            toggle.indeterminate = false;
        } else if (checkedBoxes.length > 0) {
            toggle.checked = false;
            toggle.indeterminate = true;
        } else {
            toggle.checked = false;
            toggle.indeterminate = false;
        }

        updatePermissionCount(role);
    }

    // تطبيق نماذج الصلاحيات
    function applyTemplate(template) {
        const templates = {
            'restrictive': {
                'admin': ['messages_read', 'users_read', 'departments_read', 'message_types_read', 'reports_view', 'access_admin_panel'],
                'manager': ['messages_read', 'users_read', 'departments_read', 'message_types_read', 'reports_view'],
                'secretary': ['messages_read', 'users_read', 'departments_read', 'message_types_read'],
                'employee': ['messages_read', 'departments_read', 'message_types_read']
            },
            'balanced': {
                'admin': ['messages_create', 'messages_read', 'messages_update', 'messages_delete', 'users_create', 'users_read', 'users_update', 'departments_read', 'departments_update', 'message_types_read', 'message_types_update', 'reports_view', 'reports_export', 'access_admin_panel'],
                'manager': ['messages_create', 'messages_read', 'messages_update', 'users_read', 'departments_read', 'message_types_read', 'reports_view'],
                'secretary': ['messages_create', 'messages_read', 'messages_update', 'users_read', 'departments_read', 'message_types_read'],
                'employee': ['messages_create', 'messages_read', 'departments_read', 'message_types_read']
            },
            'permissive': {
                'admin': {{ role_permissions.admin | tojson }},
                'manager': {{ role_permissions.manager | tojson }},
                'secretary': {{ role_permissions.secretary | tojson }},
                'employee': {{ role_permissions.employee | tojson }}
            }
        };

        if (confirm(`هل تريد تطبيق النموذج "${template}" على جميع الأدوار؟ سيتم إعادة تعيين جميع الصلاحيات الحالية.`)) {
            Object.keys(templates[template]).forEach(role => {
                // إلغاء تحديد جميع الصلاحيات
                document.querySelectorAll(`input[name="${role}_permissions"]`).forEach(checkbox => {
                    checkbox.checked = false;
                });

                // تحديد الصلاحيات الجديدة
                templates[template][role].forEach(permission => {
                    const checkbox = document.getElementById(`${role}-${permission}`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });

                // تحديث مفاتيح الفئات
                ['messages', 'users', 'departments', 'message_types', 'reports', 'system', 'special'].forEach(category => {
                    updateCategoryToggle(role, category);
                });
            });
        }
    }

    // حفظ جميع الصلاحيات
    function saveAllPermissions() {
        if (confirm('هل تريد حفظ جميع تغييرات الصلاحيات؟ سيتم تطبيقها على جميع المستخدمين فوراً.')) {
            const permissions = {};

            ['admin', 'manager', 'secretary', 'employee'].forEach(role => {
                permissions[role] = [];
                document.querySelectorAll(`input[name="${role}_permissions"]:checked`).forEach(checkbox => {
                    permissions[role].push(checkbox.value);
                });
            });

            // إرسال البيانات للخادم
            fetch('/admin/permissions/update-roles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(permissions)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حفظ الصلاحيات بنجاح!');
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء حفظ الصلاحيات: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ في الاتصال: ' + error);
            });
        }
    }

    // إعادة تعيين الصلاحيات
    function resetPermissions() {
        if (confirm('هل تريد إعادة تعيين جميع الصلاحيات للإعدادات الافتراضية؟ سيتم حفظها في قاعدة البيانات.')) {
            fetch('/admin/permissions/reset-defaults', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إعادة تعيين الصلاحيات للإعدادات الافتراضية بنجاح!');
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء إعادة تعيين الصلاحيات: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ في الاتصال: ' + error);
            });
        }
    }

    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث عدادات الصلاحيات
        ['admin', 'manager', 'secretary', 'employee'].forEach(role => {
            updatePermissionCount(role);

            // تحديث مفاتيح الفئات
            ['messages', 'users', 'departments', 'message_types', 'reports', 'system', 'special'].forEach(category => {
                updateCategoryToggle(role, category);
            });
        });
    });
</script>
{% endblock %}
