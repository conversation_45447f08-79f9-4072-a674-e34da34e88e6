{% extends "base.html" %}

{% block title %}إدارة الصلاحيات - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item active">إدارة الصلاحيات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-shield-alt me-2"></i>
        إدارة الصلاحيات والأدوار
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.role_permissions') }}" class="btn btn-primary">
                <i class="fas fa-user-cog me-1"></i>
                تحرير صلاحيات الأدوار
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="exportPermissions()">
                <i class="fas fa-download me-1"></i>
                تصدير الصلاحيات
            </button>
        </div>
    </div>
</div>

<!-- ملخص الأدوار -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <div class="avatar-circle mx-auto mb-2" style="width: 50px; height: 50px; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-crown text-white"></i>
                </div>
                <h5 class="card-title text-primary">مدير النظام</h5>
                <p class="card-text">صلاحيات كاملة</p>
                <small class="text-muted">{{ admin_count }} مستخدم</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <div class="avatar-circle mx-auto mb-2" style="width: 50px; height: 50px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user-tie text-white"></i>
                </div>
                <h5 class="card-title text-success">مدير</h5>
                <p class="card-text">صلاحيات إدارية</p>
                <small class="text-muted">{{ manager_count }} مستخدم</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <div class="avatar-circle mx-auto mb-2" style="width: 50px; height: 50px; background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user-edit text-white"></i>
                </div>
                <h5 class="card-title text-warning">سكرتير</h5>
                <p class="card-text">صلاحيات محدودة</p>
                <small class="text-muted">{{ secretary_count }} مستخدم</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <div class="avatar-circle mx-auto mb-2" style="width: 50px; height: 50px; background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user text-white"></i>
                </div>
                <h5 class="card-title text-info">موظف</h5>
                <p class="card-text">صلاحيات أساسية</p>
                <small class="text-muted">{{ employee_count }} مستخدم</small>
            </div>
        </div>
    </div>
</div>

<!-- جدول الصلاحيات التفصيلي -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>
            جدول الصلاحيات التفصيلي
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th style="width: 30%;">الصلاحية</th>
                        <th class="text-center">مدير النظام</th>
                        <th class="text-center">مدير</th>
                        <th class="text-center">سكرتير</th>
                        <th class="text-center">موظف</th>
                    </tr>
                </thead>
                <tbody>
                    {% set all_permissions = {
                        'إدارة الرسائل': {
                            'messages_create': 'إنشاء رسائل',
                            'messages_read': 'قراءة الرسائل',
                            'messages_update': 'تعديل الرسائل',
                            'messages_delete': 'حذف الرسائل',
                            'messages_archive': 'أرشفة الرسائل',
                            'messages_export': 'تصدير الرسائل',
                            'messages_import': 'استيراد الرسائل'
                        },
                        'إدارة المستخدمين': {
                            'users_create': 'إنشاء مستخدمين',
                            'users_read': 'عرض المستخدمين',
                            'users_update': 'تعديل المستخدمين',
                            'users_delete': 'حذف المستخدمين',
                            'users_activate': 'تفعيل/تعطيل المستخدمين',
                            'users_permissions': 'إدارة صلاحيات المستخدمين',
                            'users_reset_password': 'إعادة تعيين كلمة المرور'
                        },
                        'إدارة الأقسام': {
                            'departments_create': 'إنشاء أقسام',
                            'departments_read': 'عرض الأقسام',
                            'departments_update': 'تعديل الأقسام',
                            'departments_delete': 'حذف الأقسام'
                        },
                        'إدارة أنواع الرسائل': {
                            'message_types_create': 'إنشاء أنواع رسائل',
                            'message_types_read': 'عرض أنواع الرسائل',
                            'message_types_update': 'تعديل أنواع الرسائل',
                            'message_types_delete': 'حذف أنواع الرسائل'
                        },
                        'التقارير': {
                            'reports_view': 'عرض التقارير',
                            'reports_export': 'تصدير التقارير',
                            'reports_advanced': 'التقارير المتقدمة'
                        },
                        'إدارة النظام': {
                            'system_settings': 'إعدادات النظام',
                            'system_logs': 'سجلات النظام',
                            'system_backup': 'النسخ الاحتياطي',
                            'system_maintenance': 'صيانة النظام'
                        },
                        'صلاحيات خاصة': {
                            'view_all_messages': 'عرض جميع الرسائل',
                            'manage_all_departments': 'إدارة جميع الأقسام',
                            'access_admin_panel': 'الوصول للوحة الإدارة'
                        }
                    } %}

                    {% set role_permissions = {
                        'admin': ['messages_create', 'messages_read', 'messages_update', 'messages_delete', 'messages_archive', 'messages_export', 'messages_import', 'users_create', 'users_read', 'users_update', 'users_delete', 'users_activate', 'users_permissions', 'users_reset_password', 'departments_create', 'departments_read', 'departments_update', 'departments_delete', 'message_types_create', 'message_types_read', 'message_types_update', 'message_types_delete', 'reports_view', 'reports_export', 'reports_advanced', 'system_settings', 'system_logs', 'system_backup', 'system_maintenance', 'view_all_messages', 'manage_all_departments', 'access_admin_panel'],
                        'manager': ['messages_create', 'messages_read', 'messages_update', 'messages_archive', 'messages_export', 'users_read', 'users_update', 'departments_read', 'departments_update', 'message_types_read', 'reports_view', 'reports_export'],
                        'secretary': ['messages_create', 'messages_read', 'messages_update', 'messages_archive', 'users_read', 'departments_read', 'message_types_read', 'reports_view'],
                        'employee': ['messages_create', 'messages_read', 'users_read', 'departments_read', 'message_types_read']
                    } %}

                    {% for category, permissions in all_permissions.items() %}
                        <tr class="table-secondary">
                            <td colspan="5"><strong><i class="fas fa-folder me-2"></i>{{ category }}</strong></td>
                        </tr>
                        {% for perm_key, perm_name in permissions.items() %}
                        <tr>
                            <td class="ps-4">{{ perm_name }}</td>
                            <td class="text-center">
                                {% if perm_key in role_permissions.admin %}
                                    <i class="fas fa-check text-success" title="متاح"></i>
                                {% else %}
                                    <i class="fas fa-times text-danger" title="غير متاح"></i>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if perm_key in role_permissions.manager %}
                                    <i class="fas fa-check text-success" title="متاح"></i>
                                {% else %}
                                    <i class="fas fa-times text-danger" title="غير متاح"></i>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if perm_key in role_permissions.secretary %}
                                    <i class="fas fa-check text-success" title="متاح"></i>
                                {% else %}
                                    <i class="fas fa-times text-danger" title="غير متاح"></i>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if perm_key in role_permissions.employee %}
                                    <i class="fas fa-check text-success" title="متاح"></i>
                                {% else %}
                                    <i class="fas fa-times text-danger" title="غير متاح"></i>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- إحصائيات الصلاحيات -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الصلاحيات حسب الدور
                </h6>
            </div>
            <div class="card-body">
                <canvas id="permissionsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    توزيع المستخدمين حسب الدور
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-danger">{{ admin_count }}</h4>
                            <small class="text-muted">مديري النظام</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">{{ manager_count }}</h4>
                        <small class="text-muted">المديرين</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-warning">{{ secretary_count }}</h4>
                            <small class="text-muted">السكرتيرين</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ employee_count }}</h4>
                        <small class="text-muted">الموظفين</small>
                    </div>
                </div>

                <hr>

                <div class="progress mb-2">
                    <div class="progress-bar bg-danger" style="width: {{ (admin_count / total_users * 100) if total_users > 0 else 0 }}%"></div>
                    <div class="progress-bar bg-success" style="width: {{ (manager_count / total_users * 100) if total_users > 0 else 0 }}%"></div>
                    <div class="progress-bar bg-warning" style="width: {{ (secretary_count / total_users * 100) if total_users > 0 else 0 }}%"></div>
                    <div class="progress-bar bg-info" style="width: {{ (employee_count / total_users * 100) if total_users > 0 else 0 }}%"></div>
                </div>

                <small class="text-muted">إجمالي المستخدمين: {{ total_users }}</small>
            </div>
        </div>
    </div>
</div>

<!-- إجراءات سريعة -->
<div class="card shadow mt-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-bolt me-2"></i>
            إجراءات سريعة
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary w-100 mb-2">
                    <i class="fas fa-users me-1"></i>
                    إدارة المستخدمين
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ url_for('admin.role_permissions') }}" class="btn btn-outline-success w-100 mb-2">
                    <i class="fas fa-user-cog me-1"></i>
                    تحرير صلاحيات الأدوار
                </a>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-warning w-100 mb-2" onclick="generateReport()">
                    <i class="fas fa-chart-line me-1"></i>
                    تقرير الصلاحيات
                </button>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="backupPermissions()">
                    <i class="fas fa-download me-1"></i>
                    نسخ احتياطي
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle {
        transition: transform 0.3s ease;
    }

    .avatar-circle:hover {
        transform: scale(1.1);
    }

    .table th {
        border-top: none;
    }

    .table-secondary td {
        background-color: #f8f9fa !important;
        font-weight: bold;
    }

    .progress {
        height: 8px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // رسم بياني للصلاحيات
    const ctx = document.getElementById('permissionsChart').getContext('2d');
    const permissionsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['مدير النظام', 'مدير', 'سكرتير', 'موظف'],
            datasets: [{
                data: [{{ admin_count }}, {{ manager_count }}, {{ secretary_count }}, {{ employee_count }}],
                backgroundColor: [
                    '#dc3545',
                    '#28a745',
                    '#ffc107',
                    '#17a2b8'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // تصدير الصلاحيات
    function exportPermissions() {
        window.location.href = '/admin/permissions/export';
    }

    // مراجعة الصلاحيات
    function auditPermissions() {
        alert('سيتم تطوير هذه الميزة قريباً');
    }

    // تقرير الصلاحيات
    function generateReport() {
        window.location.href = '/reports/permissions';
    }

    // نسخ احتياطي
    function backupPermissions() {
        if (confirm('هل تريد إنشاء نسخة احتياطية من إعدادات الصلاحيات؟')) {
            alert('تم إنشاء النسخة الاحتياطية بنجاح');
        }
    }
</script>
{% endblock %}
