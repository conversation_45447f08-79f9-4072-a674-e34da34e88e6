{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item active">الملف الشخصي</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-circle me-2"></i>
        الملف الشخصي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
                <i class="fas fa-key me-1"></i>
                تغيير كلمة المرور
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات المستخدم -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات المستخدم
                </h5>
            </div>
            <div class="card-body text-center">
                <!-- صورة المستخدم -->
                <div class="mb-3">
                    <div class="avatar-circle mx-auto mb-3" style="width: 120px; height: 120px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-user fa-4x text-white"></i>
                    </div>
                </div>

                <!-- معلومات أساسية -->
                <h4 class="mb-1">{{ user.full_name }}</h4>
                <p class="text-muted mb-2">{{ user.department }}</p>

                <!-- الدور -->
                <span class="badge bg-primary mb-3">
                    {% if user.role == 'admin' %}مدير النظام
                    {% elif user.role == 'manager' %}مدير
                    {% elif user.role == 'secretary' %}سكرتير
                    {% else %}موظف{% endif %}
                </span>

                <!-- معلومات الاتصال -->
                <div class="text-start">
                    <div class="mb-2">
                        <i class="fas fa-envelope text-muted me-2"></i>
                        <span>{{ user.email }}</span>
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-user text-muted me-2"></i>
                        <span>{{ user.username }}</span>
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-calendar text-muted me-2"></i>
                        <span>عضو منذ: {{ user.created_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                    {% if user.last_login %}
                    <div class="mb-2">
                        <i class="fas fa-clock text-muted me-2"></i>
                        <span>آخر دخول: {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-0">{{ user.sent_messages.count() }}</h4>
                            <small class="text-muted">رسائل مرسلة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-0">{{ user.received_messages.count() }}</h4>
                        <small class="text-muted">رسائل مستقبلة</small>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-warning mb-0">{{ user.actions.count() }}</h4>
                            <small class="text-muted">إجراءات</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info mb-0">
                            {{ "متصل" if user.is_active else "غير متصل" }}
                        </h4>
                        <small class="text-muted">حالة الحساب</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل النشاط -->
    <div class="col-lg-8">
        <!-- الرسائل الحديثة -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل الحديثة
                </h6>
                <a href="{{ url_for('main.messages') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% set recent_messages = user.sent_messages[:5] if user.sent_messages else [] %}
                {% if recent_messages %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>الرقم المرجعي</th>
                                <th>الموضوع</th>
                                <th>المستقبل</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in recent_messages %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('main.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.reference_number }}
                                    </a>
                                </td>
                                <td>
                                    {{ message.subject[:30] }}{% if message.subject|length > 30 %}...{% endif %}
                                </td>
                                <td>
                                    {% if message.recipient %}
                                        {{ message.recipient.full_name }}
                                    {% else %}
                                        {{ message.external_recipient or 'غير محدد' }}
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge status-{{ message.status }}">
                                        {{ message.get_status_display() }}
                                    </span>
                                </td>
                                <td>
                                    <small>{{ message.created_at.strftime('%m-%d') }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا توجد رسائل حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- آخر الأنشطة -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر الأنشطة
                </h6>
            </div>
            <div class="card-body">
                {% set recent_actions = user.actions.order_by(user.actions.created_at.desc()).limit(10).all() %}
                {% if recent_actions %}
                <div class="timeline">
                    {% for action in recent_actions %}
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <i class="fas fa-circle text-primary"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="mb-1">{{ action.get_action_display() }}</h6>
                            <p class="mb-1">{{ action.description or 'لا يوجد وصف' }}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ action.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-history fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا توجد أنشطة حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- إعدادات الحساب -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات الحساب
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الأمان:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="{{ url_for('auth.change_password') }}" class="text-decoration-none">
                                    <i class="fas fa-key me-2 text-warning"></i>
                                    تغيير كلمة المرور
                                </a>
                            </li>
                            <li class="mb-2">
                                <span class="text-muted">
                                    <i class="fas fa-shield-alt me-2 text-success"></i>
                                    الحساب محمي
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>الصلاحيات:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <span class="text-muted">
                                    <i class="fas fa-{{ 'check' if user.has_permission('create') else 'times' }} me-2 text-{{ 'success' if user.has_permission('create') else 'danger' }}"></i>
                                    إنشاء رسائل
                                </span>
                            </li>
                            <li class="mb-2">
                                <span class="text-muted">
                                    <i class="fas fa-{{ 'check' if user.has_permission('update') else 'times' }} me-2 text-{{ 'success' if user.has_permission('update') else 'danger' }}"></i>
                                    تعديل رسائل
                                </span>
                            </li>
                            <li class="mb-2">
                                <span class="text-muted">
                                    <i class="fas fa-{{ 'check' if user.has_permission('view_reports') else 'times' }} me-2 text-{{ 'success' if user.has_permission('view_reports') else 'danger' }}"></i>
                                    عرض التقارير
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding-right: 20px;
    }

    .timeline-item {
        position: relative;
        padding-bottom: 20px;
        border-right: 2px solid #e9ecef;
    }

    .timeline-item:last-child {
        border-right: none;
    }

    .timeline-marker {
        position: absolute;
        right: -6px;
        top: 0;
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 50%;
        width: 12px;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .timeline-marker i {
        font-size: 6px;
    }

    .timeline-content {
        padding-right: 20px;
    }

    .avatar-circle {
        transition: transform 0.3s ease;
    }

    .avatar-circle:hover {
        transform: scale(1.05);
    }

    .card {
        transition: transform 0.2s ease;
    }

    .card:hover {
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الوقت بشكل ديناميكي
    function updateLastSeen() {
        const lastSeenElements = document.querySelectorAll('.last-seen');
        lastSeenElements.forEach(element => {
            const timestamp = element.dataset.timestamp;
            if (timestamp) {
                const date = new Date(timestamp);
                const now = new Date();
                const diff = now - date;

                // تحويل الفرق إلى وقت مقروء
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(minutes / 60);
                const days = Math.floor(hours / 24);

                let timeText = '';
                if (days > 0) {
                    timeText = `منذ ${days} يوم`;
                } else if (hours > 0) {
                    timeText = `منذ ${hours} ساعة`;
                } else if (minutes > 0) {
                    timeText = `منذ ${minutes} دقيقة`;
                } else {
                    timeText = 'الآن';
                }

                element.textContent = timeText;
            }
        });
    }

    // تحديث كل دقيقة
    setInterval(updateLastSeen, 60000);

    // تشغيل عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', updateLastSeen);
</script>
{% endblock %}
