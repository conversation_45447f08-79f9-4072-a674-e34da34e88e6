import os
import json
from datetime import datetime, timedelta
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, abort
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash
import uuid

# استيراد النماذج والإعدادات
from config import config
from models import db, User, Message, Department, MessageType, Attachment, ActionLog, Setting
from forms import LoginForm, UserForm, MessageForm, SearchForm, DepartmentForm, MessageTypeForm, ActionForm, ChangePasswordForm

def create_app(config_name='default'):
    """إنشاء تطبيق Flask"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    # تهيئة نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # إنشاء مجلد المرفقات
    upload_folder = os.path.join(app.instance_path, app.config['UPLOAD_FOLDER'])
    os.makedirs(upload_folder, exist_ok=True)
    
    # تسجيل المسارات
    from routes import auth, main, admin, api
    app.register_blueprint(auth.bp)
    app.register_blueprint(main.bp)
    app.register_blueprint(admin.bp)
    app.register_blueprint(api.bp)
    
    # إنشاء قاعدة البيانات والبيانات الأولية
    with app.app_context():
        db.create_all()
        init_database()
    
    return app

def init_database():
    """تهيئة قاعدة البيانات بالبيانات الأولية"""
    
    # إنشاء المستخدم الإداري الافتراضي
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            department='إدارة النظام',
            role='admin',
            is_active=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
    
    # إنشاء أنواع الرسائل الافتراضية
    default_message_types = [
        {'name': 'خطاب رسمي', 'name_en': 'Official Letter', 'color': '#007bff', 'priority': 1},
        {'name': 'مذكرة داخلية', 'name_en': 'Internal Memo', 'color': '#28a745', 'priority': 1},
        {'name': 'تعميم', 'name_en': 'Circular', 'color': '#ffc107', 'priority': 2},
        {'name': 'طلب', 'name_en': 'Request', 'color': '#17a2b8', 'priority': 1},
        {'name': 'شكوى', 'name_en': 'Complaint', 'color': '#dc3545', 'priority': 2},
        {'name': 'تقرير', 'name_en': 'Report', 'color': '#6f42c1', 'priority': 1},
        {'name': 'عاجل', 'name_en': 'Urgent', 'color': '#fd7e14', 'priority': 3}
    ]
    
    for msg_type_data in default_message_types:
        existing_type = MessageType.query.filter_by(name=msg_type_data['name']).first()
        if not existing_type:
            msg_type = MessageType(**msg_type_data)
            db.session.add(msg_type)
    
    # إنشاء الأقسام الافتراضية
    default_departments = [
        {'name': 'إدارة النظام', 'name_en': 'System Administration'},
        {'name': 'الموارد البشرية', 'name_en': 'Human Resources'},
        {'name': 'المالية والمحاسبة', 'name_en': 'Finance & Accounting'},
        {'name': 'تقنية المعلومات', 'name_en': 'Information Technology'},
        {'name': 'الشؤون الإدارية', 'name_en': 'Administrative Affairs'},
        {'name': 'العلاقات العامة', 'name_en': 'Public Relations'}
    ]
    
    for dept_data in default_departments:
        existing_dept = Department.query.filter_by(name=dept_data['name']).first()
        if not existing_dept:
            department = Department(**dept_data)
            db.session.add(department)
    
    # إنشاء الإعدادات الافتراضية
    default_settings = [
        {'key': 'organization_name', 'value': 'نظام المراسلات الداخلية', 'description': 'اسم المؤسسة', 'category': 'general'},
        {'key': 'organization_name_en', 'value': 'Internal Correspondence System', 'description': 'اسم المؤسسة بالإنجليزية', 'category': 'general'},
        {'key': 'auto_reference_number', 'value': 'true', 'description': 'توليد الرقم المرجعي تلقائياً', 'category': 'messages'},
        {'key': 'default_language', 'value': 'ar', 'description': 'اللغة الافتراضية', 'category': 'general'},
        {'key': 'messages_per_page', 'value': '10', 'description': 'عدد الرسائل في الصفحة', 'category': 'display'},
        {'key': 'max_file_size', 'value': '16777216', 'description': 'الحد الأقصى لحجم الملف (بايت)', 'category': 'files'},
        {'key': 'allowed_extensions', 'value': 'pdf,doc,docx,xls,xlsx,txt,jpg,jpeg,png,gif', 'description': 'امتدادات الملفات المسموحة', 'category': 'files'}
    ]
    
    for setting_data in default_settings:
        existing_setting = Setting.query.filter_by(key=setting_data['key']).first()
        if not existing_setting:
            setting = Setting(**setting_data)
            db.session.add(setting)
    
    try:
        db.session.commit()
        print("تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في تهيئة قاعدة البيانات: {e}")

def allowed_file(filename):
    """التحقق من امتداد الملف المسموح"""
    allowed_extensions = Setting.get_setting('allowed_extensions', 'pdf,doc,docx,xls,xlsx,txt,jpg,jpeg,png,gif')
    allowed_extensions = set(allowed_extensions.split(','))
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_uploaded_file(file, message_id, user_id):
    """حفظ الملف المرفوع وإنشاء سجل في قاعدة البيانات"""
    if file and allowed_file(file.filename):
        # إنشاء اسم ملف فريد
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        
        # إنشاء مسار الملف
        upload_folder = os.path.join(current_app.instance_path, current_app.config['UPLOAD_FOLDER'])
        file_path = os.path.join(upload_folder, unique_filename)
        
        # حفظ الملف
        file.save(file_path)
        
        # إنشاء سجل في قاعدة البيانات
        attachment = Attachment(
            message_id=message_id,
            filename=unique_filename,
            original_filename=filename,
            file_path=file_path,
            file_size=os.path.getsize(file_path),
            mime_type=file.content_type,
            uploaded_by=user_id
        )
        db.session.add(attachment)
        
        return attachment
    return None

def log_action(message_id, action, description=None, old_value=None, new_value=None):
    """تسجيل إجراء في سجل الأنشطة"""
    action_log = ActionLog(
        message_id=message_id,
        user_id=current_user.id,
        action=action,
        description=description,
        old_value=json.dumps(old_value) if old_value else None,
        new_value=json.dumps(new_value) if new_value else None,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    db.session.add(action_log)

if __name__ == '__main__':
    app = create_app('development')
    app.run(host='0.0.0.0', port=5000, debug=True)
