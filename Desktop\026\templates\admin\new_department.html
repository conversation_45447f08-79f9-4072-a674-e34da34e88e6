{% extends "base.html" %}

{% block title %}قسم جديد - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.departments') }}">إدارة الأقسام</a></li>
        <li class="breadcrumb-item active">قسم جديد</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus-circle me-2"></i>
        إضافة قسم جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.departments') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للأقسام
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    بيانات القسم الجديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <!-- CSRF protection will be handled by Flask-WTF if needed -->

                    <!-- المعلومات الأساسية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label required">اسم القسم</label>
                            <input type="text" class="form-control" name="name" required placeholder="اسم القسم بالعربية">
                            <div class="invalid-feedback">
                                يرجى إدخال اسم القسم
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم بالإنجليزية</label>
                            <input type="text" class="form-control" name="name_en" placeholder="Department Name">
                            <div class="form-text">اختياري - للاستخدام في التقارير</div>
                        </div>
                    </div>

                    <!-- الوصف -->
                    <div class="mb-3">
                        <label class="form-label">وصف القسم</label>
                        <textarea class="form-control" name="description" rows="3"
                                  placeholder="وصف مختصر عن القسم ومهامه..."></textarea>
                        <div class="form-text">وصف اختياري يساعد في تحديد مهام ونشاطات القسم</div>
                    </div>

                    <!-- المدير -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مدير القسم</label>
                            <select class="form-select" name="manager_id">
                                <option value="">اختر مدير القسم</option>
                                <!-- سيتم تحميل المديرين ديناميكياً -->
                                <option value="1">المدير العام</option>
                            </select>
                            <div class="form-text">يمكن تعيين مدير لاحقاً</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">الموقع</label>
                            <input type="text" class="form-control" name="location"
                                   placeholder="الطابق الأول - مبنى الإدارة">
                            <div class="form-text">موقع القسم في المبنى</div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone"
                                   placeholder="+966xxxxxxxxx">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>

                    <!-- الحالة -->
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="is_active" checked>
                            <label class="form-check-label">القسم نشط</label>
                        </div>
                        <div class="form-text">يمكن تعطيل القسم لاحقاً إذا لم يعد مستخدماً</div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.departments') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إنشاء القسم
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح:</h6>
                    <ul class="mb-0">
                        <li>اختر اسماً واضحاً ومميزاً للقسم</li>
                        <li>أضف وصفاً يوضح مهام القسم</li>
                        <li>يمكن تعيين مدير لاحقاً</li>
                        <li>تأكد من صحة معلومات الاتصال</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                    <p class="mb-0">
                        بعد إنشاء القسم، يمكن ربط المستخدمين به
                        وتعيين مدير من قائمة المستخدمين.
                    </p>
                </div>
            </div>
        </div>

        <!-- إحصائيات الأقسام -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات الأقسام
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-0">0</h4>
                            <small class="text-muted">إجمالي الأقسام</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-0">0</h4>
                        <small class="text-muted">الأقسام النشطة</small>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-warning mb-0">0</h4>
                            <small class="text-muted">لها مدير</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info mb-0">0</h4>
                        <small class="text-muted">إجمالي الموظفين</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- أقسام مقترحة -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    أقسام مقترحة
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item border-0 px-0">
                        <strong>إدارة الموارد البشرية</strong><br>
                        <small class="text-muted">إدارة شؤون الموظفين والتوظيف</small>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <strong>المالية والمحاسبة</strong><br>
                        <small class="text-muted">إدارة الشؤون المالية والمحاسبية</small>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <strong>تقنية المعلومات</strong><br>
                        <small class="text-muted">إدارة الأنظمة والتقنية</small>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <strong>الشؤون الإدارية</strong><br>
                        <small class="text-muted">الخدمات الإدارية العامة</small>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <strong>العلاقات العامة</strong><br>
                        <small class="text-muted">التواصل والإعلام</small>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">يمكنك إنشاء أقسام مخصصة حسب احتياجات مؤسستك</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .required::after {
        content: " *";
        color: red;
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    .alert {
        border-radius: 10px;
    }

    .list-group-item {
        transition: background-color 0.2s ease;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // التحقق من صحة النموذج
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // التحقق من اسم القسم
        const nameField = document.querySelector('input[name="name"]');
        nameField.addEventListener('blur', function() {
            const name = this.value.trim();
            if (name.length >= 2) {
                this.classList.add('is-valid');
            }
        });

        // التحقق من البريد الإلكتروني
        const emailField = document.querySelector('input[name="email"]');
        emailField.addEventListener('blur', function() {
            const email = this.value.trim();
            if (email === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                this.classList.add('is-valid');
            }
        });

        // التحقق من رقم الهاتف
        const phoneField = document.querySelector('input[name="phone"]');
        phoneField.addEventListener('blur', function() {
            const phone = this.value.trim();
            if (phone === '' || /^[\+]?[0-9\-\s]+$/.test(phone)) {
                this.classList.add('is-valid');
            }
        });

        // اقتراحات للأقسام
        const departmentSuggestions = [
            'إدارة الموارد البشرية',
            'المالية والمحاسبة',
            'تقنية المعلومات',
            'الشؤون الإدارية',
            'العلاقات العامة',
            'المبيعات والتسويق',
            'خدمة العملاء',
            'الإنتاج',
            'الجودة',
            'الأمن والسلامة',
            'الصيانة',
            'المشتريات',
            'المستودعات',
            'النقل واللوجستيات'
        ];

        // إضافة datalist للاقتراحات
        const datalist = document.createElement('datalist');
        datalist.id = 'departments-list';
        departmentSuggestions.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept;
            datalist.appendChild(option);
        });
        document.body.appendChild(datalist);
        nameField.setAttribute('list', 'departments-list');

        // نسخ الاقتراحات عند النقر
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.addEventListener('click', function() {
                const departmentName = this.querySelector('strong').textContent;
                nameField.value = departmentName;
                nameField.focus();
                nameField.classList.add('is-valid');
            });
        });
    });
</script>
{% endblock %}
