{% extends "base.html" %}

{% block title %}إدارة الأقسام - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item active">إدارة الأقسام</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-building me-2"></i>
        إدارة الأقسام
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.new_department') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                قسم جديد
            </a>
        </div>
    </div>
</div>

<!-- قائمة الأقسام -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الأقسام ({{ departments|length }} قسم)
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="filterDepartments('all')">
                        الكل
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="filterDepartments('active')">
                        النشطة
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="filterDepartments('inactive')">
                        غير النشطة
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                {% if departments %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم القسم</th>
                                <th>الاسم بالإنجليزية</th>
                                <th>الوصف</th>
                                <th>المدير</th>
                                <th>عدد الموظفين</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for department in departments %}
                            <tr class="department-row" data-status="{{ 'active' if department.is_active else 'inactive' }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-building text-white"></i>
                                        </div>
                                        <div>
                                            <strong>{{ department.name }}</strong>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ department.name_en or '-' }}</td>
                                <td>
                                    {% if department.description %}
                                        {{ department.description[:50] }}{% if department.description|length > 50 %}...{% endif %}
                                    {% else %}
                                        <span class="text-muted">لا يوجد وصف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if department.manager %}
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-xs bg-success rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <strong>{{ department.manager.full_name }}</strong><br>
                                                <small class="text-muted">{{ department.manager.email }}</small>
                                            </div>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">0</span>
                                </td>
                                <td>
                                    {% if department.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ department.created_at.strftime('%Y-%m-%d') if department.created_at else '-' }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('admin.edit_department', id=department.id) }}"
                                           class="btn btn-outline-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                onclick="viewDepartmentDetails({{ department.id }})" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-building fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد أقسام</h4>
                    <p class="text-muted">لم يتم إنشاء أي أقسام بعد</p>
                    <a href="{{ url_for('admin.new_department') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء قسم جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الأقسام -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ departments|length }}</h5>
                <p class="card-text">إجمالي الأقسام</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">0</h5>
                <p class="card-text">الأقسام النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">0</h5>
                <p class="card-text">أقسام لها مدير</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">0</h5>
                <p class="card-text">إجمالي الموظفين</p>
            </div>
        </div>
    </div>
</div>

<!-- مودال تفاصيل القسم -->
<div class="modal fade" id="departmentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-building me-2"></i>
                    تفاصيل القسم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="departmentDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }

    .avatar-xs {
        width: 24px;
        height: 24px;
    }

    .department-row {
        transition: all 0.3s ease;
    }

    .department-row:hover {
        background-color: rgba(0,123,255,0.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // فلترة الأقسام
    function filterDepartments(status) {
        const rows = document.querySelectorAll('.department-row');

        rows.forEach(row => {
            if (status === 'all') {
                row.style.display = '';
            } else {
                if (row.dataset.status === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });

        // تحديث الأزرار
        document.querySelectorAll('.btn-group .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    // عرض تفاصيل القسم
    function viewDepartmentDetails(departmentId) {
        // يمكن تحميل التفاصيل عبر AJAX
        const modal = new bootstrap.Modal(document.getElementById('departmentDetailsModal'));
        const content = document.getElementById('departmentDetailsContent');

        content.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل القسم...</p>
            </div>
        `;

        modal.show();

        // محاكاة تحميل البيانات
        setTimeout(() => {
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات أساسية:</h6>
                        <ul class="list-unstyled">
                            <li><strong>اسم القسم:</strong> قسم تجريبي</li>
                            <li><strong>الاسم بالإنجليزية:</strong> Test Department</li>
                            <li><strong>الحالة:</strong> <span class="badge bg-success">نشط</span></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>إحصائيات:</h6>
                        <ul class="list-unstyled">
                            <li><strong>عدد الموظفين:</strong> 5</li>
                            <li><strong>عدد الرسائل:</strong> 25</li>
                            <li><strong>تاريخ الإنشاء:</strong> 2024-01-01</li>
                        </ul>
                    </div>
                </div>
                <hr>
                <h6>الوصف:</h6>
                <p>هذا قسم تجريبي لأغراض العرض والاختبار.</p>
            `;
        }, 1000);
    }

    // البحث في الأقسام
    function searchDepartments() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const rows = document.querySelectorAll('.department-row');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // إضافة حقل البحث
    document.addEventListener('DOMContentLoaded', function() {
        const cardHeader = document.querySelector('.card-header');
        const searchDiv = document.createElement('div');
        searchDiv.className = 'col-md-4';
        searchDiv.innerHTML = `
            <div class="input-group">
                <input type="text" class="form-control" id="search-input" placeholder="البحث في الأقسام..." onkeyup="searchDepartments()">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
            </div>
        `;

        // إعادة تنظيم الهيدر
        const headerContent = cardHeader.innerHTML;
        cardHeader.innerHTML = `
            <div class="row align-items-center w-100">
                <div class="col-md-8">
                    ${headerContent}
                </div>
                <div class="col-md-4">
                    ${searchDiv.innerHTML}
                </div>
            </div>
        `;
    });
</script>
{% endblock %}
