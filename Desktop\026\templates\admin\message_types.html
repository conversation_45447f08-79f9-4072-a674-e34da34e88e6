{% extends "base.html" %}

{% block title %}أنواع الرسائل - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item active">أنواع الرسائل</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tags me-2"></i>
        أنواع الرسائل
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.new_message_type') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                نوع جديد
            </a>
        </div>
    </div>
</div>

<!-- قائمة أنواع الرسائل -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة أنواع الرسائل ({{ message_types|length }} نوع)
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="filterTypes('all')">
                        الكل
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="filterTypes('active')">
                        النشطة
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="filterTypes('inactive')">
                        غير النشطة
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                {% if message_types %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>النوع</th>
                                <th>الاسم بالإنجليزية</th>
                                <th>اللون</th>
                                <th>الأولوية</th>
                                <th>عدد الرسائل</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message_type in message_types %}
                            <tr class="type-row" data-status="{{ 'active' if message_type.is_active else 'inactive' }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="color-indicator me-3"
                                             style="width: 20px; height: 20px; background-color: {{ message_type.color }}; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;">
                                        </div>
                                        <div>
                                            <strong>{{ message_type.name }}</strong>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ message_type.name_en or '-' }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="color-preview me-2"
                                             style="width: 30px; height: 20px; background-color: {{ message_type.color }}; border-radius: 4px; border: 1px solid #ddd;">
                                        </div>
                                        <code>{{ message_type.color }}</code>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge
                                        {% if message_type.priority >= 80 %}bg-danger
                                        {% elif message_type.priority >= 60 %}bg-warning
                                        {% elif message_type.priority >= 40 %}bg-info
                                        {% else %}bg-secondary{% endif %}">
                                        {{ message_type.priority }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-primary">0</span>
                                </td>
                                <td>
                                    {% if message_type.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ message_type.created_at.strftime('%Y-%m-%d') if message_type.created_at else '-' }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                onclick="previewMessageType('{{ message_type.name }}', '{{ message_type.color }}')" title="معاينة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد أنواع رسائل</h4>
                    <p class="text-muted">لم يتم إنشاء أي أنواع رسائل بعد</p>
                    <a href="{{ url_for('admin.new_message_type') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء نوع جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- أنواع الرسائل الافتراضية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    أنواع رسائل مقترحة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card border">
                            <div class="card-body text-center p-3">
                                <div class="color-circle mx-auto mb-2" style="width: 40px; height: 40px; background-color: #007bff; border-radius: 50%;"></div>
                                <h6 class="mb-1">رسمية</h6>
                                <small class="text-muted">للمراسلات الرسمية</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border">
                            <div class="card-body text-center p-3">
                                <div class="color-circle mx-auto mb-2" style="width: 40px; height: 40px; background-color: #28a745; border-radius: 50%;"></div>
                                <h6 class="mb-1">إدارية</h6>
                                <small class="text-muted">للشؤون الإدارية</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border">
                            <div class="card-body text-center p-3">
                                <div class="color-circle mx-auto mb-2" style="width: 40px; height: 40px; background-color: #ffc107; border-radius: 50%;"></div>
                                <h6 class="mb-1">مالية</h6>
                                <small class="text-muted">للأمور المالية</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border">
                            <div class="card-body text-center p-3">
                                <div class="color-circle mx-auto mb-2" style="width: 40px; height: 40px; background-color: #dc3545; border-radius: 50%;"></div>
                                <h6 class="mb-1">عاجلة</h6>
                                <small class="text-muted">للرسائل العاجلة</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <small class="text-muted">يمكنك إنشاء أنواع مخصصة حسب احتياجات مؤسستك</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات أنواع الرسائل -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ message_types|length }}</h5>
                <p class="card-text">إجمالي الأنواع</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">
                    {{ message_types | selectattr('is_active') | list | length }}
                </h5>
                <p class="card-text">الأنواع النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">
                    {% set high_priority = message_types | selectattr('priority', 'ge', 80) | list | length %}
                    {{ high_priority }}
                </h5>
                <p class="card-text">أولوية عالية</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">0</h5>
                <p class="card-text">إجمالي الرسائل</p>
            </div>
        </div>
    </div>
</div>

<!-- مودال معاينة النوع -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>
                    معاينة نوع الرسالة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center" id="previewContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .color-indicator {
        transition: transform 0.2s ease;
    }

    .color-indicator:hover {
        transform: scale(1.2);
    }

    .color-preview {
        transition: all 0.2s ease;
    }

    .color-preview:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .type-row {
        transition: all 0.3s ease;
    }

    .type-row:hover {
        background-color: rgba(0,123,255,0.1);
    }

    .color-circle {
        transition: transform 0.2s ease;
    }

    .color-circle:hover {
        transform: scale(1.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // فلترة أنواع الرسائل
    function filterTypes(status) {
        const rows = document.querySelectorAll('.type-row');

        rows.forEach(row => {
            if (status === 'all') {
                row.style.display = '';
            } else {
                if (row.dataset.status === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });

        // تحديث الأزرار
        document.querySelectorAll('.btn-group .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    // معاينة نوع الرسالة
    function previewMessageType(name, color) {
        const modal = new bootstrap.Modal(document.getElementById('previewModal'));
        const content = document.getElementById('previewContent');

        content.innerHTML = `
            <div class="mb-4">
                <div class="color-circle mx-auto mb-3" style="width: 80px; height: 80px; background-color: ${color}; border-radius: 50%; border: 4px solid #fff; box-shadow: 0 4px 12px rgba(0,0,0,0.2);"></div>
                <h4>${name}</h4>
            </div>

            <div class="alert alert-light">
                <h6>مثال على الاستخدام:</h6>
                <div class="badge" style="background-color: ${color}; color: white; font-size: 14px; padding: 8px 16px;">
                    ${name}
                </div>
            </div>

            <div class="row text-center">
                <div class="col-6">
                    <h6>اللون</h6>
                    <code>${color}</code>
                </div>
                <div class="col-6">
                    <h6>النوع</h6>
                    <span>${name}</span>
                </div>
            </div>
        `;

        modal.show();
    }

    // البحث في أنواع الرسائل
    function searchTypes() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const rows = document.querySelectorAll('.type-row');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // إضافة حقل البحث
    document.addEventListener('DOMContentLoaded', function() {
        const cardHeader = document.querySelector('.card-header');
        const searchDiv = document.createElement('div');
        searchDiv.className = 'col-md-4';
        searchDiv.innerHTML = `
            <div class="input-group">
                <input type="text" class="form-control" id="search-input" placeholder="البحث في أنواع الرسائل..." onkeyup="searchTypes()">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
            </div>
        `;

        // إعادة تنظيم الهيدر
        const headerContent = cardHeader.innerHTML;
        cardHeader.innerHTML = `
            <div class="row align-items-center w-100">
                <div class="col-md-8">
                    ${headerContent}
                </div>
                <div class="col-md-4">
                    ${searchDiv.innerHTML}
                </div>
            </div>
        `;
    });
</script>
{% endblock %}
