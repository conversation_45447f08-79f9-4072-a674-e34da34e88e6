{% extends "base.html" %}

{% block title %}سجل الأنشطة - نظام المراسلات الداخلية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">لوحة الإدارة</a></li>
        <li class="breadcrumb-item active">سجل الأنشطة</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-history me-2"></i>
        سجل الأنشطة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-danger" onclick="clearOldLogs()">
                <i class="fas fa-trash me-1"></i>
                مسح السجلات القديمة
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="exportLogs()">
                <i class="fas fa-download me-1"></i>
                تصدير السجل
            </button>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر البحث
                </h6>
            </div>
            <div class="card-body">
                <form id="filterForm" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">المستخدم</label>
                        <select class="form-select" name="user_id">
                            <option value="">جميع المستخدمين</option>
                            <!-- سيتم تحميل المستخدمين ديناميكياً -->
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">نوع النشاط</label>
                        <select class="form-select" name="action_type">
                            <option value="">جميع الأنشطة</option>
                            <option value="login">تسجيل دخول</option>
                            <option value="logout">تسجيل خروج</option>
                            <option value="create">إنشاء</option>
                            <option value="update">تعديل</option>
                            <option value="delete">حذف</option>
                            <option value="view">عرض</option>
                            <option value="download">تحميل</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="date_from">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="date_to">
                    </div>
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-search me-1"></i>
                            تطبيق الفلاتر
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- سجل الأنشطة -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    سجل الأنشطة ({{ logs.total if logs else 0 }} نشاط)
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary active" onclick="setView('table')">
                        <i class="fas fa-table"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="setView('timeline')">
                        <i class="fas fa-stream"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- عرض الجدول -->
                <div id="tableView">
                    {% if logs and logs.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>التوقيت</th>
                                    <th>المستخدم</th>
                                    <th>النشاط</th>
                                    <th>الوصف</th>
                                    <th>عنوان IP</th>
                                    <th>المتصفح</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs.items %}
                                <tr>
                                    <td>
                                        <small>{{ log.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        {% if log.user %}
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-xs bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <strong>{{ log.user.full_name }}</strong><br>
                                                <small class="text-muted">{{ log.user.department }}</small>
                                            </div>
                                        </div>
                                        {% else %}
                                        <span class="text-muted">نظام</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if log.action == 'login' %}bg-success
                                            {% elif log.action == 'logout' %}bg-secondary
                                            {% elif log.action == 'create' %}bg-primary
                                            {% elif log.action == 'update' %}bg-warning
                                            {% elif log.action == 'delete' %}bg-danger
                                            {% elif log.action == 'view' %}bg-info
                                            {% else %}bg-light text-dark{% endif %}">
                                            {{ log.get_action_display() }}
                                        </span>
                                    </td>
                                    <td>{{ log.description or 'لا يوجد وصف' }}</td>
                                    <td>
                                        <code>{{ log.ip_address or '-' }}</code>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ log.user_agent[:30] if log.user_agent else '-' }}{% if log.user_agent and log.user_agent|length > 30 %}...{% endif %}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    {% if logs.pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="تصفح السجلات">
                            <ul class="pagination justify-content-center mb-0">
                                {% if logs.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.logs', page=logs.prev_num) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in logs.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != logs.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('admin.logs', page=page_num) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if logs.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.logs', page=logs.next_num) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد أنشطة</h4>
                        <p class="text-muted">لم يتم تسجيل أي أنشطة بعد</p>
                    </div>
                    {% endif %}
                </div>

                <!-- عرض الخط الزمني -->
                <div id="timelineView" style="display: none;">
                    <div class="p-4">
                        {% if logs and logs.items %}
                        <div class="timeline">
                            {% for log in logs.items %}
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-circle
                                        {% if log.action == 'login' %}text-success
                                        {% elif log.action == 'logout' %}text-secondary
                                        {% elif log.action == 'create' %}text-primary
                                        {% elif log.action == 'update' %}text-warning
                                        {% elif log.action == 'delete' %}text-danger
                                        {% else %}text-info{% endif %}"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">{{ log.get_action_display() }}</h6>
                                            <p class="mb-1">{{ log.description or 'لا يوجد وصف' }}</p>
                                            {% if log.user %}
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>{{ log.user.full_name }}
                                            </small>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">
                                            {{ log.created_at.strftime('%H:%M') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-stream fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد أنشطة</h4>
                            <p class="text-muted">لم يتم تسجيل أي أنشطة بعد</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الأنشطة -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ logs.total if logs else 0 }}</h5>
                <p class="card-text">إجمالي الأنشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">0</h5>
                <p class="card-text">تسجيلات دخول</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">0</h5>
                <p class="card-text">عمليات إنشاء</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">0</h5>
                <p class="card-text">عمليات تعديل</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-xs {
        width: 24px;
        height: 24px;
    }

    .timeline {
        position: relative;
        padding-right: 20px;
    }

    .timeline-item {
        position: relative;
        padding-bottom: 20px;
        border-right: 2px solid #e9ecef;
    }

    .timeline-item:last-child {
        border-right: none;
    }

    .timeline-marker {
        position: absolute;
        right: -6px;
        top: 0;
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 50%;
        width: 12px;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .timeline-marker i {
        font-size: 6px;
    }

    .timeline-content {
        padding-right: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تطبيق الفلاتر
    function applyFilters() {
        const form = document.getElementById('filterForm');
        const formData = new FormData(form);
        const params = new URLSearchParams();

        for (let [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }

        window.location.href = `{{ url_for('admin.logs') }}?${params.toString()}`;
    }

    // مسح الفلاتر
    function clearFilters() {
        document.getElementById('filterForm').reset();
        window.location.href = `{{ url_for('admin.logs') }}`;
    }

    // تغيير العرض
    function setView(viewType) {
        const tableView = document.getElementById('tableView');
        const timelineView = document.getElementById('timelineView');
        const buttons = document.querySelectorAll('.btn-group .btn');

        buttons.forEach(btn => btn.classList.remove('active'));

        if (viewType === 'table') {
            tableView.style.display = 'block';
            timelineView.style.display = 'none';
            event.target.classList.add('active');
        } else {
            tableView.style.display = 'none';
            timelineView.style.display = 'block';
            event.target.classList.add('active');
        }
    }

    // مسح السجلات القديمة
    function clearOldLogs() {
        if (confirm('هل أنت متأكد من مسح السجلات القديمة؟\n\nسيتم حذف جميع السجلات الأقدم من 30 يوماً.')) {
            // محاكاة مسح السجلات
            showAlert('تم مسح السجلات القديمة بنجاح!', 'success');
        }
    }

    // تصدير السجلات
    function exportLogs() {
        showAlert('جاري تصدير السجلات...', 'info');

        // محاكاة تصدير السجلات
        setTimeout(() => {
            showAlert('تم تصدير السجلات بنجاح!', 'success');
        }, 2000);
    }

    // إظهار التنبيهات
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }

    // تحديث تلقائي للسجلات كل دقيقة
    setInterval(() => {
        // يمكن إضافة تحديث تلقائي هنا
    }, 60000);
</script>
{% endblock %}
